<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle actions
$action = $_GET['action'] ?? '';
$message = $_SESSION['message'] ?? '';
$message_type = $_SESSION['message_type'] ?? '';

// Clear session messages after retrieving them
if (isset($_SESSION['message'])) {
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'verify_enrollment') {
        $enrollment_id = $_POST['enrollment_id'];
        $verification_status = $_POST['verification_status'];
        $verification_notes = $_POST['verification_notes'];

        try {
            $stmt = $conn->prepare("UPDATE enrollments SET verification_status = ?, verification_notes = ?, verified_at = NOW(), verified_by = ? WHERE id = ?");
            $stmt->execute([$verification_status, $verification_notes, $_SESSION['user_id'], $enrollment_id]);
            $_SESSION['message'] = "Enrollment verification updated successfully!";
            $_SESSION['message_type'] = "success";
            header('Location: registrar.php');
            exit();
        } catch (Exception $e) {
            $_SESSION['message'] = "Error updating verification: " . $e->getMessage();
            $_SESSION['message_type'] = "error";
            header('Location: registrar.php');
            exit();
        }
    }
}

// Handle GET actions
if ($action === 'bulk_verify') {
    try {
        $stmt = $conn->prepare("UPDATE enrollments SET verification_status = 'verified', verified_at = NOW(), verified_by = ? WHERE verification_status IS NULL OR verification_status = 'pending'");
        $stmt->execute([$_SESSION['user_id']]);
        $affected = $stmt->rowCount();
        $_SESSION['message'] = "Successfully verified $affected enrollments!";
        $_SESSION['message_type'] = "success";
        header('Location: registrar.php');
        exit();
    } catch (Exception $e) {
        $_SESSION['message'] = "Error in bulk verification: " . $e->getMessage();
        $_SESSION['message_type'] = "error";
        header('Location: registrar.php');
        exit();
    }
} elseif ($action === 'export_verified') {
    // Export verified enrollments to CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="verified_enrollments_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');
    fputcsv($output, ['Student ID', 'Student Name', 'Course', 'Semester', 'School Year', 'Verification Status', 'Verified Date', 'Verified By']);

    $verified_stmt = $conn->prepare("SELECT e.*, u.student_id, u.first_name, u.last_name, c.course_code, v.first_name as verifier_first, v.last_name as verifier_last
                                    FROM enrollments e
                                    JOIN users u ON e.student_id = u.id
                                    LEFT JOIN courses c ON u.course_id = c.id
                                    LEFT JOIN users v ON e.verified_by = v.id
                                    WHERE e.verification_status = 'verified'
                                    ORDER BY e.verified_at DESC");
    $verified_stmt->execute();
    $verified_enrollments = $verified_stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($verified_enrollments as $enrollment) {
        fputcsv($output, [
            $enrollment['student_id'],
            $enrollment['first_name'] . ' ' . $enrollment['last_name'],
            $enrollment['course_code'],
            getSemesterName($enrollment['semester']),
            $enrollment['school_year'],
            $enrollment['verification_status'],
            $enrollment['verified_at'] ? date('M d, Y', strtotime($enrollment['verified_at'])) : '',
            $enrollment['verifier_first'] ? $enrollment['verifier_first'] . ' ' . $enrollment['verifier_last'] : ''
        ]);
    }
    fclose($output);
    exit;
} elseif ($action === 'generate_official_list') {
    // Generate official enrollment list
    require_once '../includes/official_list_generator.php';
    generateOfficialEnrollmentList($conn);
    exit;
} elseif ($action === 'generate_certification' && isset($_GET['student_id'])) {
    // Generate enrollment certification
    $student_id = $_GET['student_id'];
    require_once '../includes/certification_generator.php';
    generateEnrollmentCertification($conn, $student_id);
    exit;
}

// Get enrollment statistics
$stats = [];

// Total enrollments
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments");
$stmt->execute();
$stats['total_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Verified enrollments
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE verification_status = 'verified'");
$stmt->execute();
$stats['verified_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Pending verification
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE verification_status IS NULL OR verification_status = 'pending'");
$stmt->execute();
$stats['pending_verification'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Rejected enrollments
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE verification_status = 'rejected'");
$stmt->execute();
$stats['rejected_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Get enrollments for verification
$search = $_GET['search'] ?? '';
$status_filter = $_GET['status_filter'] ?? '';

$enrollments_query = "SELECT e.*, u.student_id, u.first_name, u.last_name, u.email, c.course_code, c.course_name 
                      FROM enrollments e 
                      JOIN users u ON e.student_id = u.id 
                      LEFT JOIN courses c ON u.course_id = c.id 
                      WHERE 1=1";

$params = [];
if ($search) {
    $enrollments_query .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.student_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_fill(0, 3, $search_param);
}

if ($status_filter) {
    if ($status_filter === 'pending') {
        $enrollments_query .= " AND (e.verification_status IS NULL OR e.verification_status = 'pending')";
    } else {
        $enrollments_query .= " AND e.verification_status = ?";
        $params[] = $status_filter;
    }
}

$enrollments_query .= " ORDER BY e.submitted_at DESC";

$enrollments_stmt = $conn->prepare($enrollments_query);
$enrollments_stmt->execute($params);
$enrollments = $enrollments_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Registrar Office";
?>

<?php include '../includes/admin_layout_start.php'; ?>

<!-- Main Content -->
<div class="flex-1 p-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-purple-600 to-indigo-600 rounded-2xl shadow-xl text-white p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Registrar Office</h1>
                    <p class="text-purple-100 text-lg">Official enrollment verification and document management</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-stamp text-4xl text-white opacity-80"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
            <div class="flex items-center">
                <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Enrollments</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo number_format($stats['total_enrollments']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clipboard-list text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Verified</p>
                    <h3 class="text-2xl font-bold text-green-600"><?php echo number_format($stats['verified_enrollments']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-check-circle text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Pending Verification</p>
                    <h3 class="text-2xl font-bold text-yellow-600"><?php echo number_format($stats['pending_verification']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Rejected</p>
                    <h3 class="text-2xl font-bold text-red-600"><?php echo number_format($stats['rejected_enrollments']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-times-circle text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="flex flex-wrap gap-4 mb-6">
        <button onclick="generateOfficialList()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
            <i class="fas fa-list mr-2"></i>Official Enrollment List
        </button>
        <button onclick="generateCertification()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <i class="fas fa-certificate mr-2"></i>Enrollment Certification
        </button>
        <button onclick="exportVerifiedList()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <i class="fas fa-download mr-2"></i>Export Verified List
        </button>
        <button onclick="bulkVerification()" class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700 transition-colors flex items-center">
            <i class="fas fa-check-double mr-2"></i>Bulk Verification
        </button>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by student name or ID..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
            </div>
            <div class="min-w-48">
                <select name="status_filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                    <option value="">All Status</option>
                    <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending Verification</option>
                    <option value="verified" <?php echo $status_filter === 'verified' ? 'selected' : ''; ?>>Verified</option>
                    <option value="rejected" <?php echo $status_filter === 'rejected' ? 'selected' : ''; ?>>Rejected</option>
                </select>
            </div>
            <button type="submit" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            <a href="registrar.php" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
        </form>
    </div>

    <!-- Enrollments Table -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Enrollment Verification</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if (empty($enrollments)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-clipboard-list text-2xl text-gray-400"></i>
                    </div>
                    <p class="text-gray-500">No enrollments found.</p>
                </div>
            <?php else: ?>
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Student</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Course</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Period</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Status</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Verification</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Date Submitted</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($enrollments as $enrollment): ?>
                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['last_name'] . ', ' . $enrollment['first_name']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['email']); ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['course_code']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['course_name']); ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="text-gray-900"><?php echo getSemesterName($enrollment['semester']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo $enrollment['school_year']; ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <?php
                                    $status_class = '';
                                    switch ($enrollment['status']) {
                                        case 'approved': $status_class = 'bg-green-100 text-green-800'; break;
                                        case 'pending': $status_class = 'bg-yellow-100 text-yellow-800'; break;
                                        case 'returned': $status_class = 'bg-red-100 text-red-800'; break;
                                    }
                                    ?>
                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $status_class; ?>">
                                        <?php echo ucfirst($enrollment['status']); ?>
                                    </span>
                                </td>
                                <td class="py-4 px-6">
                                    <?php
                                    $verification_status = $enrollment['verification_status'] ?? 'pending';
                                    $verification_class = '';
                                    switch ($verification_status) {
                                        case 'verified': $verification_class = 'bg-green-100 text-green-800'; break;
                                        case 'rejected': $verification_class = 'bg-red-100 text-red-800'; break;
                                        default: $verification_class = 'bg-yellow-100 text-yellow-800'; break;
                                    }
                                    ?>
                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $verification_class; ?>">
                                        <?php echo ucfirst($verification_status); ?>
                                    </span>
                                </td>
                                <td class="py-4 px-6 text-gray-700"><?php echo date('M d, Y', strtotime($enrollment['submitted_at'])); ?></td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button onclick="viewEnrollmentDetails(<?php echo $enrollment['id']; ?>)" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors" title="View Details">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button onclick="verifyEnrollment(<?php echo $enrollment['id']; ?>)" class="bg-purple-100 text-purple-700 px-3 py-1 rounded-lg text-sm hover:bg-purple-200 transition-colors" title="Verify">
                                            <i class="fas fa-stamp"></i>
                                        </button>
                                        <?php if ($enrollment['status'] === 'approved' && $enrollment['verification_status'] === 'verified'): ?>
                                            <button onclick="generateCOR(<?php echo $enrollment['id']; ?>)" class="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm hover:bg-green-200 transition-colors" title="Generate COR">
                                                <i class="fas fa-file-pdf"></i>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Verification Modal -->
<div id="verificationModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-800">Verify Enrollment</h3>
                    <button onclick="closeVerificationModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <form id="verificationForm" method="POST" action="?action=verify_enrollment" class="p-6" onsubmit="return handleVerificationSubmit(this)">
                <input type="hidden" name="enrollment_id" id="verification_enrollment_id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Verification Status</label>
                        <select name="verification_status" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500">
                            <option value="verified">Verified</option>
                            <option value="rejected">Rejected</option>
                            <option value="pending">Pending</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Verification Notes</label>
                        <textarea name="verification_notes" rows="4"
                                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                                  placeholder="Enter verification notes or reasons for rejection..."></textarea>
                    </div>
                </div>
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="closeVerificationModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="verificationSubmitBtn" class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                        Update Verification
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function verifyEnrollment(enrollmentId) {
    if (!enrollmentId) {
        alert('Invalid enrollment ID');
        return;
    }
    document.getElementById('verification_enrollment_id').value = enrollmentId;
    document.getElementById('verificationModal').classList.remove('hidden');
}

function closeVerificationModal() {
    document.getElementById('verificationModal').classList.add('hidden');
    // Reset submit button
    const submitBtn = document.getElementById('verificationSubmitBtn');
    if (submitBtn) {
        submitBtn.innerHTML = 'Update Verification';
        submitBtn.disabled = false;
    }
}

function handleVerificationSubmit(form) {
    const submitBtn = document.getElementById('verificationSubmitBtn');
    const enrollmentId = form.querySelector('input[name="enrollment_id"]').value;
    const verificationStatus = form.querySelector('select[name="verification_status"]').value;

    // Validate inputs
    if (!enrollmentId) {
        alert('Invalid enrollment ID');
        return false;
    }

    if (!verificationStatus) {
        alert('Please select verification status');
        return false;
    }

    // Show loading state
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
        submitBtn.disabled = true;
    }

    // Allow form submission
    return true;
}

function viewEnrollmentDetails(enrollmentId) {
    window.open('review-enrollment.php?id=' + enrollmentId, '_blank');
}

function generateCOR(enrollmentId) {
    window.open('generate-cor.php?id=' + enrollmentId, '_blank');
}

function generateOfficialList() {
    window.open('?action=generate_official_list', '_blank');
}

function generateCertification() {
    const studentId = prompt('Enter Student ID for enrollment certification:');
    if (studentId) {
        window.open('?action=generate_certification&student_id=' + studentId, '_blank');
    }
}

function exportVerifiedList() {
    window.location.href = '?action=export_verified';
}

function bulkVerification() {
    if (confirm('Are you sure you want to verify all pending enrollments? This action will verify all enrollments that are currently pending verification.')) {
        // Show loading state
        const button = event.target;
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        button.disabled = true;

        window.location.href = '?action=bulk_verify';
    }
}

// Close modal when clicking outside
document.getElementById('verificationModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeVerificationModal();
    }
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
