<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get enrollment ID
$enrollment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Verify enrollment belongs to current user and is approved
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.middle_name, u.student_id, u.year_level,
                       c.course_code, c.course_name 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE e.id = ? AND e.student_id = ? AND e.status = 'approved'");
$stmt->execute([$enrollment_id, $_SESSION['user_id']]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: dashboard.php');
    exit();
}

// Get enrolled subjects
$stmt = $conn->prepare("SELECT s.* FROM subjects s 
                       JOIN enrollment_subjects es ON s.id = es.subject_id 
                       WHERE es.enrollment_id = ? 
                       ORDER BY s.subject_code");
$stmt->execute([$enrollment_id]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Generate COR HTML
$cor_html = generateCORHTML($enrollment, $subjects);

// If download parameter is set, force download
if (isset($_GET['download'])) {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="COR_' . $enrollment['student_id'] . '_' . $enrollment['semester'] . '_' . str_replace('-', '_', $enrollment['school_year']) . '.pdf"');
    
    // For now, we'll output HTML. In a real implementation, you'd use a PDF library like TCPDF or DomPDF
    echo $cor_html;
    exit();
}

function generateCORHTML($enrollment, $subjects) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Certificate of Registration</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background: white;
            }
            .cor-container {
                max-width: 800px;
                margin: 0 auto;
                border: 2px solid #000;
                padding: 20px;
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #000;
                padding-bottom: 20px;
            }
            .school-name {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 5px;
            }
            .school-address {
                font-size: 14px;
                margin-bottom: 10px;
            }
            .document-title {
                font-size: 20px;
                font-weight: bold;
                margin-top: 15px;
            }
            .student-info {
                margin-bottom: 20px;
            }
            .info-row {
                display: flex;
                margin-bottom: 8px;
            }
            .info-label {
                width: 150px;
                font-weight: bold;
            }
            .info-value {
                flex: 1;
                border-bottom: 1px solid #000;
                padding-bottom: 2px;
            }
            .subjects-table {
                width: 100%;
                border-collapse: collapse;
                margin: 20px 0;
            }
            .subjects-table th,
            .subjects-table td {
                border: 1px solid #000;
                padding: 8px;
                text-align: left;
            }
            .subjects-table th {
                background-color: #f0f0f0;
                font-weight: bold;
            }
            .text-center {
                text-align: center;
            }
            .text-right {
                text-align: right;
            }
            .summary {
                margin-top: 20px;
                border-top: 2px solid #000;
                padding-top: 15px;
            }
            .signatures {
                margin-top: 40px;
                display: flex;
                justify-content: space-between;
            }
            .signature-block {
                text-align: center;
                width: 200px;
            }
            .signature-line {
                border-bottom: 1px solid #000;
                margin-bottom: 5px;
                height: 40px;
            }
            @media print {
                body { margin: 0; padding: 10px; }
                .cor-container { border: 2px solid #000; }
            }
        </style>
    </head>
    <body>
        <div class="cor-container">
            <!-- Header -->
            <div class="header">
                <div class="school-name">MASBATE COLLEGES</div>
                <div class="school-address">Masbate City, Masbate, Philippines</div>
                <div class="school-address">Tel: (************* | Email: <EMAIL></div>
                <div class="document-title">CERTIFICATE OF REGISTRATION</div>
            </div>
            
            <!-- Student Information -->
            <div class="student-info">
                <div class="info-row">
                    <div class="info-label">Student Name:</div>
                    <div class="info-value">
                        <?php echo strtoupper(htmlspecialchars($enrollment['last_name'] . ', ' . $enrollment['first_name'] . ' ' . ($enrollment['middle_name'] ? $enrollment['middle_name'] : ''))); ?>
                    </div>
                </div>
                <div class="info-row">
                    <div class="info-label">Student ID:</div>
                    <div class="info-value"><?php echo htmlspecialchars($enrollment['student_id']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Course:</div>
                    <div class="info-value"><?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Year Level:</div>
                    <div class="info-value"><?php echo $enrollment['year_level']; ?><?php echo getOrdinalSuffix($enrollment['year_level']); ?> Year</div>
                </div>
                <div class="info-row">
                    <div class="info-label">Semester:</div>
                    <div class="info-value"><?php echo getSemesterName($enrollment['semester']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">School Year:</div>
                    <div class="info-value"><?php echo $enrollment['school_year']; ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Date Enrolled:</div>
                    <div class="info-value"><?php echo date('F d, Y', strtotime($enrollment['approved_at'])); ?></div>
                </div>
            </div>
            
            <!-- Subjects Table -->
            <table class="subjects-table">
                <thead>
                    <tr>
                        <th>Subject Code</th>
                        <th>Subject Description</th>
                        <th class="text-center">Units</th>
                        <th>Prerequisite</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($subjects as $subject): ?>
                        <tr>
                            <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                            <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                            <td class="text-center"><?php echo $subject['units']; ?></td>
                            <td><?php echo $subject['prerequisite'] ? htmlspecialchars($subject['prerequisite']) : 'None'; ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
            <!-- Summary -->
            <div class="summary">
                <div class="info-row">
                    <div class="info-label">Total Units:</div>
                    <div class="info-value text-center" style="width: 100px;"><?php echo $enrollment['total_units']; ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Total Fees:</div>
                    <div class="info-value text-center" style="width: 150px;"><?php echo formatCurrency($enrollment['total_fees']); ?></div>
                </div>
                <div class="info-row">
                    <div class="info-label">Status:</div>
                    <div class="info-value text-center" style="width: 100px;">ENROLLED</div>
                </div>
            </div>
            
            <!-- Signatures -->
            <div class="signatures">
                <div class="signature-block">
                    <div class="signature-line"></div>
                    <div>Student Signature</div>
                </div>
                <div class="signature-block">
                    <div class="signature-line"></div>
                    <div>Registrar</div>
                </div>
            </div>
            
            <!-- Footer -->
            <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
                <p>This is an official document of Northern Samar Colleges.</p>
                <p>Generated on: <?php echo date('F d, Y g:i A'); ?></p>
            </div>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}



$page_title = "Certificate of Registration";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .cor-container {
                display: block !important;
                margin: 0 !important;
                padding: 20px !important;
            }

            body {
                margin: 0 !important;
                padding: 0 !important;
            }
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl no-print">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Header -->
            <div class="flex items-center justify-between mb-8 no-print">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-file-alt text-2xl text-blue-600"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Certificate of Registration</h1>
                        <p class="text-gray-600">Official enrollment document</p>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button onclick="window.print()" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium btn-hover">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                    <a href="?id=<?php echo $enrollment_id; ?>&download=1" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium btn-hover">
                        <i class="fas fa-download mr-2"></i>Download PDF
                    </a>
                    <a href="dashboard.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back
                    </a>
                </div>
            </div>

            <!-- COR Content -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div class="p-0">
                    <?php echo $cor_html; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn-hover');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 1000);
            }
        });
    });
});
</script>

</body>
</html>
