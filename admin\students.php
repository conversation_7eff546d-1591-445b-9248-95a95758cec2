<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'update_status') {
            $student_id = (int)$_POST['student_id'];
            $status = $_POST['status'];
            
            $stmt = $conn->prepare("UPDATE users SET status = ? WHERE id = ? AND user_type = 'student'");
            if ($stmt->execute([$status, $student_id])) {
                $success_message = 'Student status updated successfully!';
                logActivity($_SESSION['user_id'], 'student_status_updated', "Updated student ID: $student_id status to: $status");
            } else {
                $error_message = 'Failed to update student status.';
            }
        }
    }
}

// Get filter parameters
$course_filter = isset($_GET['course']) ? (int)$_GET['course'] : 0;
$year_filter = isset($_GET['year']) ? (int)$_GET['year'] : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$search = isset($_GET['search']) ? trim($_GET['search']) : '';

// Build query with filters
$where_conditions = ["u.user_type = 'student'"];
$params = [];

if ($course_filter > 0) {
    $where_conditions[] = "u.course_id = ?";
    $params[] = $course_filter;
}
if ($year_filter > 0) {
    $where_conditions[] = "u.year_level = ?";
    $params[] = $year_filter;
}
if (!empty($status_filter)) {
    $where_conditions[] = "u.status = ?";
    $params[] = $status_filter;
}
if (!empty($search)) {
    $where_conditions[] = "(u.student_id LIKE ? OR u.first_name LIKE ? OR u.last_name LIKE ? OR u.email LIKE ?)";
    $search_param = "%$search%";
    $params = array_merge($params, [$search_param, $search_param, $search_param, $search_param]);
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

// Get students with course information
$stmt = $conn->prepare("SELECT u.*, c.course_code, c.course_name,
                       (SELECT COUNT(*) FROM enrollments e WHERE e.student_id = u.id) as enrollment_count,
                       (SELECT COUNT(*) FROM enrollments e WHERE e.student_id = u.id AND e.status = 'approved') as approved_count
                       FROM users u 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       $where_clause
                       ORDER BY u.student_id");
$stmt->execute($params);
$students = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all courses for dropdowns (ensure unique courses)
$stmt = $conn->prepare("SELECT * FROM courses WHERE status = 'active' GROUP BY course_code, course_name ORDER BY course_code");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Student Management";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-user-graduate text-2xl text-nsc-primary"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Student Management</h1>
                        <p class="text-gray-600">Manage student accounts and information</p>
                    </div>
                </div>
            </div>

            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Filters and Search -->
            <div class="bg-white rounded-2xl shadow-lg mb-8">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-filter text-blue-600"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Search & Filter Students</h2>
                    </div>
                </div>
                <div class="p-6">
                    <form method="GET" action="" class="space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                            <div class="lg:col-span-2">
                                <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                                <input type="text"
                                       name="search"
                                       id="search"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                                       value="<?php echo htmlspecialchars($search); ?>"
                                       placeholder="Student ID, Name, or Email">
                            </div>
                            <div>
                                <label for="course" class="block text-sm font-medium text-gray-700 mb-2">Course</label>
                                <select name="course"
                                        id="course"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                    <option value="">All Courses</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['course_code']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div>
                                <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Year Level</label>
                                <select name="year"
                                        id="year"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                    <option value="">All Years</option>
                                    <option value="1" <?php echo $year_filter == 1 ? 'selected' : ''; ?>>1st Year</option>
                                    <option value="2" <?php echo $year_filter == 2 ? 'selected' : ''; ?>>2nd Year</option>
                                    <option value="3" <?php echo $year_filter == 3 ? 'selected' : ''; ?>>3rd Year</option>
                                    <option value="4" <?php echo $year_filter == 4 ? 'selected' : ''; ?>>4th Year</option>
                                </select>
                            </div>
                            <div>
                                <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select name="status"
                                        id="status"
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                    <option value="">All Status</option>
                                    <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>Active</option>
                                    <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                </select>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-3">
                            <button type="submit" class="bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                                <i class="fas fa-search mr-2"></i>Search
                            </button>
                            <a href="students.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <i class="fas fa-times mr-2"></i>Clear
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Students Table -->
            <div class="bg-white rounded-2xl shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-users text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Students (<?php echo count($students); ?> found)</h2>
                        </div>
                        <div class="flex space-x-2">
                            <a href="reports.php" class="border border-blue-300 text-blue-700 px-4 py-2 rounded-lg hover:bg-blue-50 transition-colors font-medium">
                                <i class="fas fa-download mr-2"></i>Export Report
                            </a>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <?php if (empty($students)): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-user-graduate text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-gray-600">No students found matching your criteria.</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Student ID</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Name</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Email</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Course</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Year</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Contact</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Enrollments</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <?php foreach ($students as $student): ?>
                                        <tr class="hover:bg-gray-50 transition-colors">
                                            <td class="py-4 px-4">
                                                <span class="font-semibold text-gray-900"><?php echo htmlspecialchars($student['student_id']); ?></span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <span class="text-gray-900 font-medium"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></span>
                                                    <?php if ($student['middle_name']): ?>
                                                        <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['middle_name']); ?></div>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <a href="mailto:<?php echo htmlspecialchars($student['email']); ?>" class="text-blue-600 hover:text-blue-800 transition-colors">
                                                    <?php echo htmlspecialchars($student['email']); ?>
                                                </a>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <span class="text-gray-900 font-medium"><?php echo htmlspecialchars($student['course_code']); ?></span>
                                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($student['course_name']); ?></div>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <span class="text-gray-700"><?php echo $student['year_level']; ?><?php echo getOrdinalSuffix($student['year_level']); ?></span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <span class="text-gray-700"><?php echo htmlspecialchars($student['contact_number'] ?: 'N/A'); ?></span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div class="flex flex-col gap-1">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 w-fit">
                                                        <?php echo $student['enrollment_count']; ?> total
                                                    </span>
                                                    <?php if ($student['approved_count'] > 0): ?>
                                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 w-fit">
                                                            <?php echo $student['approved_count']; ?> approved
                                                        </span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $student['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                    <?php echo ucfirst($student['status']); ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div class="flex space-x-2">
                                                    <button type="button" class="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                                                            onclick="viewStudent(<?php echo htmlspecialchars(json_encode($student)); ?>)" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </button>
                                                    <button type="button" class="text-yellow-600 hover:text-yellow-800 p-2 rounded-lg hover:bg-yellow-50 transition-colors"
                                                            onclick="updateStatus(<?php echo $student['id']; ?>, '<?php echo $student['status']; ?>', '<?php echo htmlspecialchars($student['student_id']); ?>')" title="Update Status">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="enrollments.php?student=<?php echo $student['id']; ?>"
                                                       class="text-green-600 hover:text-green-800 p-2 rounded-lg hover:bg-green-50 transition-colors" title="View Enrollments">
                                                        <i class="fas fa-list"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            </div>

<!-- Student Details Modal -->
<div id="studentModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Student Details</h3>
                    <button onclick="closeStudentModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6" id="studentDetails">
                <!-- Student details will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Update Status Modal -->
<div id="statusModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Update Student Status</h3>
                    <button onclick="closeStatusModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form id="statusForm" method="POST">
                    <input type="hidden" name="action" value="update_status">
                    <input type="hidden" name="student_id" id="statusStudentId">

                    <div class="mb-4">
                        <p class="text-gray-700 mb-2">Student: <span id="statusStudentName" class="font-semibold"></span></p>
                        <p class="text-gray-700 mb-4">Current Status: <span id="currentStatus" class="font-semibold"></span></p>
                    </div>

                    <div class="mb-6">
                        <label for="newStatus" class="block text-sm font-medium text-gray-700 mb-2">New Status</label>
                        <select name="status" id="newStatus" class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>

                    <div class="flex space-x-3">
                        <button type="submit" class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                            Update Status
                        </button>
                        <button type="button" onclick="closeStatusModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>

function viewStudent(student) {
    const modal = document.getElementById('studentModal');
    const detailsContainer = document.getElementById('studentDetails');

    if (!modal) {
        alert('Error: Modal not found. Please refresh the page.');
        return;
    }

    if (!detailsContainer) {
        alert('Error: Details container not found. Please refresh the page.');
        return;
    }

    detailsContainer.innerHTML = `
        <div class="space-y-4">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Student ID:</span>
                <span class="text-gray-900">${student.student_id}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Name:</span>
                <span class="text-gray-900">${student.first_name} ${student.last_name}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Email:</span>
                <span class="text-gray-900">${student.email}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Course:</span>
                <span class="text-gray-900">${student.course_code || 'Not Set'}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Year Level:</span>
                <span class="text-gray-900">${student.year_level}${getOrdinalSuffix(student.year_level)}</span>
            </div>
            <hr class="border-gray-200">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-700">Status:</span>
                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${student.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                    ${student.status.charAt(0).toUpperCase() + student.status.slice(1)}
                </span>
            </div>
        </div>
    `;

    modal.classList.remove('hidden');
}

function closeStudentModal() {
    document.getElementById('studentModal').classList.add('hidden');
}

function updateStatus(studentId, currentStatus, studentName) {
    const modal = document.getElementById('statusModal');

    document.getElementById('statusStudentId').value = studentId;
    document.getElementById('statusStudentName').textContent = studentName;
    document.getElementById('currentStatus').textContent = currentStatus.charAt(0).toUpperCase() + currentStatus.slice(1);
    document.getElementById('newStatus').value = currentStatus;

    modal.classList.remove('hidden');
}

function closeStatusModal() {
    document.getElementById('statusModal').classList.add('hidden');
}

function getOrdinalSuffix(number) {
    const ends = ['th','st','nd','rd','th','th','th','th','th','th'];
    if ((number % 100) >= 11 && (number % 100) <= 13)
        return 'th';
    else
        return ends[number % 10];
}

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    const studentModal = document.getElementById('studentModal');
    const statusModal = document.getElementById('statusModal');

    if (e.target === studentModal) {
        closeStudentModal();
    }
    if (e.target === statusModal) {
        closeStatusModal();
    }
});
</script>



<?php include '../includes/admin_layout_end.php'; ?>
