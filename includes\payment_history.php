<?php
function showPaymentHistory($conn, $enrollment_id) {
    // Get enrollment and student info
    $enrollment_stmt = $conn->prepare("SELECT e.*, u.student_id, u.first_name, u.last_name, c.course_code 
                                      FROM enrollments e 
                                      JOIN users u ON e.student_id = u.id 
                                      LEFT JOIN courses c ON u.course_id = c.id 
                                      WHERE e.id = ?");
    $enrollment_stmt->execute([$enrollment_id]);
    $enrollment = $enrollment_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$enrollment) {
        echo "Enrollment not found!";
        return;
    }
    
    // Get payments for this enrollment
    $payments_stmt = $conn->prepare("SELECT p.*, u.first_name as recorder_first, u.last_name as recorder_last 
                                    FROM payments p 
                                    LEFT JOIN users u ON p.recorded_by = u.id 
                                    WHERE p.enrollment_id = ? 
                                    ORDER BY p.payment_date DESC, p.created_at DESC");
    $payments_stmt->execute([$enrollment_id]);
    $payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate totals
    $total_paid = array_sum(array_column($payments, 'amount'));
    $balance = $enrollment['total_fees'] - $total_paid;
    
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment History - <?php echo htmlspecialchars($enrollment['student_id']); ?></title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            'nsc-primary': '#16a34a',
                            'nsc-secondary': '#22c55e'
                        }
                    }
                }
            }
        </script>
    </head>
    <body class="bg-gray-50">
        <div class="max-w-6xl mx-auto p-6">
            <!-- Header -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-800">Payment History</h1>
                        <p class="text-gray-600">Student: <?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?> (<?php echo htmlspecialchars($enrollment['student_id']); ?>)</p>
                    </div>
                    <button onclick="window.print()" class="bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors">
                        <i class="fas fa-print mr-2"></i>Print
                    </button>
                </div>
            </div>

            <!-- Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">Total Fees</p>
                            <h3 class="text-2xl font-bold text-gray-800"><?php echo formatCurrency($enrollment['total_fees']); ?></h3>
                        </div>
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-invoice-dollar text-blue-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">Total Paid</p>
                            <h3 class="text-2xl font-bold text-green-600"><?php echo formatCurrency($total_paid); ?></h3>
                        </div>
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
                        </div>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-gray-500 text-sm">Balance</p>
                            <h3 class="text-2xl font-bold <?php echo $balance > 0 ? 'text-red-600' : 'text-green-600'; ?>">
                                <?php echo formatCurrency($balance); ?>
                            </h3>
                        </div>
                        <div class="w-12 h-12 <?php echo $balance > 0 ? 'bg-red-100' : 'bg-green-100'; ?> rounded-lg flex items-center justify-center">
                            <i class="fas <?php echo $balance > 0 ? 'fa-exclamation-triangle text-red-600' : 'fa-check-circle text-green-600'; ?> text-xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment History Table -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-800">Payment Transactions</h2>
                </div>
                <div class="overflow-x-auto">
                    <?php if (empty($payments)): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-receipt text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-gray-500">No payments recorded yet.</p>
                        </div>
                    <?php else: ?>
                        <table class="w-full">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Date</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Amount</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Payment Method</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Reference</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Status</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Recorded By</th>
                                    <th class="text-left py-3 px-6 font-semibold text-gray-700">Notes</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($payments as $payment): ?>
                                    <tr class="border-b border-gray-100 hover:bg-gray-50">
                                        <td class="py-4 px-6">
                                            <div>
                                                <p class="font-semibold text-gray-900"><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></p>
                                                <p class="text-sm text-gray-500"><?php echo date('h:i A', strtotime($payment['created_at'])); ?></p>
                                            </div>
                                        </td>
                                        <td class="py-4 px-6">
                                            <span class="text-lg font-semibold text-green-600"><?php echo formatCurrency($payment['amount']); ?></span>
                                        </td>
                                        <td class="py-4 px-6">
                                            <span class="px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                                                <?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-6 text-gray-700">
                                            <?php echo htmlspecialchars($payment['reference_number'] ?: 'N/A'); ?>
                                        </td>
                                        <td class="py-4 px-6">
                                            <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $payment['status'] === 'confirmed' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                                <?php echo ucfirst($payment['status']); ?>
                                            </span>
                                        </td>
                                        <td class="py-4 px-6 text-gray-700">
                                            <?php echo htmlspecialchars($payment['recorder_first'] . ' ' . $payment['recorder_last']); ?>
                                        </td>
                                        <td class="py-4 px-6 text-gray-700">
                                            <?php echo htmlspecialchars($payment['notes'] ?: 'No notes'); ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Course Information -->
            <div class="bg-white rounded-xl shadow-lg p-6 mt-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">Enrollment Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <p class="text-gray-600"><strong>Course:</strong> <?php echo htmlspecialchars($enrollment['course_code']); ?></p>
                        <p class="text-gray-600"><strong>School Year:</strong> <?php echo htmlspecialchars($enrollment['school_year']); ?></p>
                    </div>
                    <div>
                        <p class="text-gray-600"><strong>Semester:</strong> <?php echo getSemesterName($enrollment['semester']); ?></p>
                        <p class="text-gray-600"><strong>Enrollment Status:</strong> 
                            <span class="px-2 py-1 rounded text-sm <?php echo $enrollment['status'] === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'; ?>">
                                <?php echo ucfirst($enrollment['status']); ?>
                            </span>
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <style>
            @media print {
                body { background: white !important; }
                .no-print { display: none !important; }
            }
        </style>
    </body>
    </html>
    <?php
}
?>
