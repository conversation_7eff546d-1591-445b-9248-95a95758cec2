<?php
// Insert sample data for testing
$host = "localhost";
$username = "root";
$password = "";
$database = "masbate_enrollment";

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Inserting Sample Data</h2>";
    
    // Insert sample students
    $students = [
        ['student_id' => '20240001', 'email' => '<EMAIL>', 'first_name' => '<PERSON>', 'last_name' => 'Dela Cruz', 'course_id' => 1, 'year_level' => 1],
        ['student_id' => '20240002', 'email' => '<EMAIL>', 'first_name' => 'Maria', 'last_name' => '<PERSON>', 'course_id' => 1, 'year_level' => 2],
        ['student_id' => '20240003', 'email' => '<EMAIL>', 'first_name' => '<PERSON>', 'last_name' => '<PERSON>', 'course_id' => 2, 'year_level' => 1],
    ];
    
    foreach ($students as $student) {
        try {
            $password_hash = password_hash('student123', PASSWORD_DEFAULT);
            $stmt = $pdo->prepare("INSERT IGNORE INTO users (student_id, email, password, first_name, last_name, course_id, year_level, user_type) VALUES (?, ?, ?, ?, ?, ?, ?, 'student')");
            $stmt->execute([$student['student_id'], $student['email'], $password_hash, $student['first_name'], $student['last_name'], $student['course_id'], $student['year_level']]);
            echo "<p>✓ Added student: {$student['first_name']} {$student['last_name']}</p>";
        } catch (Exception $e) {
            echo "<p>⚠ Student {$student['first_name']} {$student['last_name']} already exists</p>";
        }
    }
    
    // Insert sample enrollments
    $current_year = date('Y');
    $school_year = $current_year . '-' . ($current_year + 1);
    
    $enrollments = [
        ['student_id' => 1, 'semester' => 1, 'school_year' => $school_year, 'total_units' => 15, 'total_fees' => 9500.00, 'status' => 'approved'],
        ['student_id' => 2, 'semester' => 1, 'school_year' => $school_year, 'total_units' => 18, 'total_fees' => 11000.00, 'status' => 'pending'],
        ['student_id' => 3, 'semester' => 1, 'school_year' => $school_year, 'total_units' => 15, 'total_fees' => 9500.00, 'status' => 'approved'],
    ];
    
    foreach ($enrollments as $enrollment) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO enrollments (student_id, semester, school_year, total_units, total_fees, status, submitted_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$enrollment['student_id'], $enrollment['semester'], $enrollment['school_year'], $enrollment['total_units'], $enrollment['total_fees'], $enrollment['status']]);
            echo "<p>✓ Added enrollment for student ID: {$enrollment['student_id']}</p>";
        } catch (Exception $e) {
            echo "<p>⚠ Enrollment for student ID {$enrollment['student_id']} may already exist</p>";
        }
    }
    
    // Insert sample grades
    $grades = [
        ['student_id' => 1, 'subject_id' => 1, 'semester' => 1, 'school_year' => $school_year, 'grade' => 85.50, 'remarks' => 'PASSED'],
        ['student_id' => 1, 'subject_id' => 2, 'semester' => 1, 'school_year' => $school_year, 'grade' => 90.00, 'remarks' => 'PASSED'],
        ['student_id' => 1, 'subject_id' => 3, 'semester' => 1, 'school_year' => $school_year, 'grade' => 78.75, 'remarks' => 'PASSED'],
        ['student_id' => 2, 'subject_id' => 1, 'semester' => 1, 'school_year' => $school_year, 'grade' => 92.00, 'remarks' => 'PASSED'],
        ['student_id' => 2, 'subject_id' => 2, 'semester' => 1, 'school_year' => $school_year, 'grade' => 88.25, 'remarks' => 'PASSED'],
    ];
    
    foreach ($grades as $grade) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO student_grades (student_id, subject_id, semester, school_year, grade, remarks, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$grade['student_id'], $grade['subject_id'], $grade['semester'], $grade['school_year'], $grade['grade'], $grade['remarks']]);
            echo "<p>✓ Added grade for student ID: {$grade['student_id']}, subject ID: {$grade['subject_id']}</p>";
        } catch (Exception $e) {
            echo "<p>⚠ Grade for student ID {$grade['student_id']}, subject ID {$grade['subject_id']} may already exist</p>";
        }
    }
    
    // Insert sample payments
    $payments = [
        ['enrollment_id' => 1, 'amount' => 5000.00, 'payment_method' => 'cash', 'reference_number' => 'CASH001', 'payment_date' => date('Y-m-d'), 'recorded_by' => 1],
        ['enrollment_id' => 1, 'amount' => 4500.00, 'payment_method' => 'gcash', 'reference_number' => 'GC123456789', 'payment_date' => date('Y-m-d', strtotime('+1 day')), 'recorded_by' => 1],
        ['enrollment_id' => 3, 'amount' => 9500.00, 'payment_method' => 'bank_transfer', 'reference_number' => 'BT987654321', 'payment_date' => date('Y-m-d'), 'recorded_by' => 1],
    ];
    
    foreach ($payments as $payment) {
        try {
            $stmt = $pdo->prepare("INSERT IGNORE INTO payments (enrollment_id, amount, payment_method, reference_number, payment_date, recorded_by, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$payment['enrollment_id'], $payment['amount'], $payment['payment_method'], $payment['reference_number'], $payment['payment_date'], $payment['recorded_by']]);
            echo "<p>✓ Added payment for enrollment ID: {$payment['enrollment_id']}</p>";
        } catch (Exception $e) {
            echo "<p>⚠ Payment for enrollment ID {$payment['enrollment_id']} may already exist</p>";
        }
    }
    
    echo "<h3>Sample data insertion completed!</h3>";
    echo "<p><a href='test_functionality.php'>Run Functionality Test</a> | <a href='dashboard.php'>Go to Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
