<?php
// Reset Installation Script for Masbate Colleges Online Enrollment System

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['confirm_reset'])) {
    try {
        // Connect to database
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Drop database if exists
        $pdo->exec("DROP DATABASE IF EXISTS masbate_enrollment");
        
        // Remove flag file
        if (file_exists('database_initialized.flag')) {
            unlink('database_initialized.flag');
        }
        
        $success = true;
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Reset Installation - Masbate Colleges Online Enrollment System</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen flex items-center justify-center p-4">
    <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
        <h1 class="text-2xl font-bold text-red-600 mb-4">Reset Installation</h1>
        
        <?php if (isset($success)): ?>
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                Installation has been reset successfully!
                <br><a href="install.php" class="text-blue-600 underline">Go to Installation</a>
            </div>
        <?php elseif (isset($error)): ?>
            <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                Error: <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
            <strong>Warning:</strong> This will completely remove the database and all data!
        </div>
        
        <form method="POST">
            <div class="mb-4">
                <label class="flex items-center">
                    <input type="checkbox" name="confirm_reset" value="1" class="mr-2" required>
                    I understand this will delete all data
                </label>
            </div>
            <button type="submit" class="w-full bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700">
                Reset Installation
            </button>
        </form>
        
        <div class="mt-4 text-center">
            <a href="index.php" class="text-blue-600 underline">Back to Homepage</a>
        </div>
    </div>
</body>
</html>
