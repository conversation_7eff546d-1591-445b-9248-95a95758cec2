<?php
// Masbate Colleges Online Enrollment System - Installation Script

$error_message = '';
$success_message = '';
$installation_complete = false;

// Check if already installed
if (file_exists('database_initialized.flag')) {
    $installation_complete = true;
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$installation_complete) {
    try {
        // Initialize database directly
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS masbate_enrollment");
        $pdo->exec("USE masbate_enrollment");

        // Create tables
        createTables($pdo);

        // Insert sample data
        insertSampleData($pdo);

        // Create uploads directory
        if (!is_dir('uploads/payments')) {
            mkdir('uploads/payments', 0755, true);
        }

        // Create flag file
        file_put_contents('database_initialized.flag', 'Database initialized on ' . date('Y-m-d H:i:s'));

        $success_message = 'Installation completed successfully!';
        $installation_complete = true;

    } catch (Exception $e) {
        $error_message = 'Installation error: ' . $e->getMessage();
    }
}

function createTables($pdo) {
    // Create users table
    $pdo->exec("CREATE TABLE IF NOT EXISTS users (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id VARCHAR(20) UNIQUE,
        email VARCHAR(100) UNIQUE NOT NULL,
        password VARCHAR(255) NOT NULL,
        first_name VARCHAR(50) NOT NULL,
        last_name VARCHAR(50) NOT NULL,
        middle_name VARCHAR(50),
        contact_number VARCHAR(15),
        address TEXT,
        course_id INT,
        year_level INT,
        user_type ENUM('student', 'admin') DEFAULT 'student',
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Create courses table
    $pdo->exec("CREATE TABLE IF NOT EXISTS courses (
        id INT AUTO_INCREMENT PRIMARY KEY,
        course_code VARCHAR(10) NOT NULL,
        course_name VARCHAR(100) NOT NULL,
        description TEXT,
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");

    // Create subjects table
    $pdo->exec("CREATE TABLE IF NOT EXISTS subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        subject_code VARCHAR(20) NOT NULL,
        subject_name VARCHAR(100) NOT NULL,
        units INT NOT NULL,
        course_id INT,
        year_level INT,
        semester INT,
        prerequisite VARCHAR(100),
        status ENUM('active', 'inactive') DEFAULT 'active',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (course_id) REFERENCES courses(id)
    )");

    // Create enrollments table
    $pdo->exec("CREATE TABLE IF NOT EXISTS enrollments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        semester INT NOT NULL,
        school_year VARCHAR(20) NOT NULL,
        total_units INT DEFAULT 0,
        total_fees DECIMAL(10,2) DEFAULT 0.00,
        payment_proof VARCHAR(255),
        status ENUM('pending', 'approved', 'returned') DEFAULT 'pending',
        remarks TEXT,
        submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        approved_at TIMESTAMP NULL,
        approved_by INT NULL,
        FOREIGN KEY (student_id) REFERENCES users(id),
        FOREIGN KEY (approved_by) REFERENCES users(id)
    )");

    // Create enrollment_subjects table
    $pdo->exec("CREATE TABLE IF NOT EXISTS enrollment_subjects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        enrollment_id INT NOT NULL,
        subject_id INT NOT NULL,
        FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE,
        FOREIGN KEY (subject_id) REFERENCES subjects(id)
    )");

    // Create activity_logs table
    $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");

    // Create notifications table
    $pdo->exec("CREATE TABLE IF NOT EXISTS notifications (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        subject VARCHAR(255) NOT NULL,
        message TEXT NOT NULL,
        is_read BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users(id)
    )");
}

function insertSampleData($pdo) {
    // Insert default admin user
    $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT IGNORE INTO users (student_id, email, password, first_name, last_name, user_type) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute(['ADMIN001', '<EMAIL>', $admin_password, 'System', 'Administrator', 'admin']);

    // Insert sample student - First check if it already exists
    $check_student = $pdo->prepare("SELECT COUNT(*) FROM users WHERE email = ? OR student_id = ?");
    $check_student->execute(['<EMAIL>', '20240001']);
    $existing_student = $check_student->fetchColumn();

    if ($existing_student == 0) {
        $student_password = password_hash('student123', PASSWORD_DEFAULT);
        $stmt = $pdo->prepare("INSERT INTO users (student_id, email, password, first_name, last_name, middle_name, contact_number, address, course_id, year_level, user_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute(['20240001', '<EMAIL>', $student_password, 'Juan', 'Dela Cruz', 'Santos', '09123456789', 'Sample Address, Masbate City, Masbate', null, 1, 'student']);

        // Check if student was inserted
        if (!$result) {
            throw new Exception("Failed to insert sample student: " . implode(", ", $stmt->errorInfo()));
        }
    }

    // Final verification that student exists
    $verify_stmt = $pdo->prepare("SELECT COUNT(*) FROM users WHERE user_type = 'student' AND email = '<EMAIL>'");
    $verify_stmt->execute();
    $student_exists = $verify_stmt->fetchColumn();

    if ($student_exists == 0) {
        throw new Exception("Sample student verification failed - student was not created");
    }

    // Note: Other tables (courses, subjects, enrollments, etc.) will remain empty
    // These can be populated by the admin through the system interface
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - Masbate Colleges Online Enrollment System</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #3b82f6 50%, #60a5fa 100%);
        }

        .floating {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
    </style>
</head>
<body class="font-sans antialiased gradient-bg min-h-screen flex items-center justify-center p-4">
    <!-- Floating background elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-20 left-10 w-72 h-72 bg-white/10 rounded-full blur-3xl floating"></div>
        <div class="absolute bottom-20 right-10 w-96 h-96 bg-white/5 rounded-full blur-3xl floating" style="animation-delay: -3s;"></div>
    </div>

    <div class="relative max-w-2xl mx-auto">
        <div class="bg-white rounded-3xl shadow-2xl p-8 animate-slide-up">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-br from-nsc-primary to-nsc-secondary rounded-full flex items-center justify-center mx-auto mb-4 floating">
                    <i class="fas fa-graduation-cap text-3xl text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-nsc-primary mb-2">Masbate Colleges</h1>
                <h2 class="text-xl text-gray-700 mb-2">Online Enrollment System</h2>
                <p class="text-gray-500">Installation Setup</p>
            </div>
                    
            <!-- Alert Messages -->
            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg animate-fade-in">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg animate-fade-in">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>
                    
            <?php if ($installation_complete): ?>
                <!-- Installation Complete -->
                <div class="text-center">
                    <div class="bg-green-100 border border-green-400 rounded-2xl p-6 mb-6">
                        <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-check text-2xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-green-800 mb-2">Installation Complete!</h3>
                        <p class="text-green-700">The system has been successfully installed and configured.</p>
                    </div>

                    <!-- Credentials Card -->
                    <div class="bg-blue-50 border border-blue-200 rounded-2xl p-6 mb-6">
                        <h4 class="text-lg font-semibold text-blue-900 mb-4 flex items-center justify-center">
                            <i class="fas fa-key mr-2"></i>Default Login Credentials
                        </h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-white rounded-lg p-4 border border-blue-200">
                                <h5 class="font-semibold text-gray-900 mb-2">Admin Account</h5>
                                <p class="text-sm text-gray-600">Email: <span class="font-mono text-blue-600"><EMAIL></span></p>
                                <p class="text-sm text-gray-600">Password: <span class="font-mono text-blue-600">admin123</span></p>
                            </div>
                            <div class="bg-white rounded-lg p-4 border border-blue-200">
                                <h5 class="font-semibold text-gray-900 mb-2">Sample Student</h5>
                                <p class="text-sm text-gray-600">Email: <span class="font-mono text-blue-600"><EMAIL></span></p>
                                <p class="text-sm text-gray-600">Password: <span class="font-mono text-blue-600">student123</span></p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="index.php" class="bg-gradient-to-r from-nsc-primary to-nsc-secondary text-white px-8 py-3 rounded-lg font-semibold hover:from-nsc-secondary hover:to-nsc-primary transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-home mr-2"></i>Go to Homepage
                        </a>
                        <a href="login.php" class="border-2 border-nsc-primary text-nsc-primary px-8 py-3 rounded-lg font-semibold hover:bg-nsc-primary hover:text-white transition-all duration-300">
                            <i class="fas fa-sign-in-alt mr-2"></i>Login Now
                        </a>
                    </div>
                </div>
                    <?php else: ?>
                        <div class="mb-4">
                            <h5>System Requirements Check</h5>
                            <ul class="list-group">
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PHP Version (>= 7.4)
                                    <?php if (version_compare(PHP_VERSION, '7.4.0') >= 0): ?>
                                        <span class="badge bg-success"><i class="fas fa-check"></i> <?php echo PHP_VERSION; ?></span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><i class="fas fa-times"></i> <?php echo PHP_VERSION; ?></span>
                                    <?php endif; ?>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    PDO MySQL Extension
                                    <?php if (extension_loaded('pdo_mysql')): ?>
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Available</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><i class="fas fa-times"></i> Not Available</span>
                                    <?php endif; ?>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    File Upload Support
                                    <?php if (ini_get('file_uploads')): ?>
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Enabled</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><i class="fas fa-times"></i> Disabled</span>
                                    <?php endif; ?>
                                </li>
                                <li class="list-group-item d-flex justify-content-between align-items-center">
                                    Uploads Directory Writable
                                    <?php 
                                    $uploads_writable = is_writable('.') || mkdir('uploads', 0755, true);
                                    ?>
                                    <?php if ($uploads_writable): ?>
                                        <span class="badge bg-success"><i class="fas fa-check"></i> Writable</span>
                                    <?php else: ?>
                                        <span class="badge bg-danger"><i class="fas fa-times"></i> Not Writable</span>
                                    <?php endif; ?>
                                </li>
                            </ul>
                        </div>
                        
                        <div class="mb-4">
                            <h5>Database Configuration</h5>
                            <div class="alert alert-info">
                                <p><strong>Database Settings:</strong></p>
                                <ul class="mb-0">
                                    <li>Host: localhost</li>
                                    <li>Database: nsc_enrollment (will be created)</li>
                                    <li>Username: root</li>
                                    <li>Password: (empty)</li>
                                </ul>
                                <p class="mt-2 mb-0"><small>Make sure MySQL/MariaDB is running and accessible with these credentials.</small></p>
                            </div>
                        </div>
                        
                        <form method="POST" action="">
                            <div class="d-grid">
                                <button type="submit" class="btn btn-nsc-primary btn-lg">
                                    <i class="fas fa-cog me-2"></i>Install System
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                    
                    <div class="text-center mt-4">
                        <small class="text-muted">
                            Masbate Colleges Online Enrollment System v1.0<br>
                            &copy; <?php echo date('Y'); ?> All rights reserved.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
