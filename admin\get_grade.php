<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

header('Content-Type: application/json');

if (!isset($_GET['id'])) {
    echo json_encode(['success' => false, 'message' => 'Grade ID not provided']);
    exit;
}

$db = new Database();
$conn = $db->getConnection();

try {
    $stmt = $conn->prepare("SELECT sg.*, u.first_name, u.last_name, u.student_id, s.subject_code, s.subject_name 
                           FROM student_grades sg 
                           JOIN users u ON sg.student_id = u.id 
                           JOIN subjects s ON sg.subject_id = s.id 
                           WHERE sg.id = ?");
    $stmt->execute([$_GET['id']]);
    $grade = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($grade) {
        echo json_encode(['success' => true, 'grade' => $grade]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Grade not found']);
    }
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
