<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle actions
$action = $_GET['action'] ?? '';
$message = $_SESSION['message'] ?? '';
$message_type = $_SESSION['message_type'] ?? '';

// Clear session messages after retrieving them
if (isset($_SESSION['message'])) {
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add_grade') {
        $student_id = $_POST['student_id'];
        $subject_id = $_POST['subject_id'];
        $semester = $_POST['semester'];
        $school_year = $_POST['school_year'];
        $grade = $_POST['grade'];
        $remarks = $_POST['remarks'];

        try {
            $stmt = $conn->prepare("INSERT INTO student_grades (student_id, subject_id, semester, school_year, grade, remarks, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$student_id, $subject_id, $semester, $school_year, $grade, $remarks]);
            $_SESSION['message'] = "Grade added successfully!";
            $_SESSION['message_type'] = "success";
            header('Location: records.php');
            exit();
        } catch (Exception $e) {
            $_SESSION['message'] = "Error adding grade: " . $e->getMessage();
            $_SESSION['message_type'] = "error";
            header('Location: records.php');
            exit();
        }
    } elseif ($action === 'edit_grade') {
        $grade_id = $_POST['grade_id'];
        $grade = $_POST['grade'];
        $remarks = $_POST['remarks'];

        try {
            $stmt = $conn->prepare("UPDATE student_grades SET grade = ?, remarks = ?, updated_at = NOW() WHERE id = ?");
            $stmt->execute([$grade, $remarks, $grade_id]);
            $_SESSION['message'] = "Grade updated successfully!";
            $_SESSION['message_type'] = "success";
            header('Location: records.php');
            exit();
        } catch (Exception $e) {
            $_SESSION['message'] = "Error updating grade: " . $e->getMessage();
            $_SESSION['message_type'] = "error";
            header('Location: records.php');
            exit();
        }
    }
}

// Handle GET actions
if ($action === 'delete_grade' && isset($_GET['id'])) {
    try {
        $stmt = $conn->prepare("DELETE FROM student_grades WHERE id = ?");
        $stmt->execute([$_GET['id']]);
        $_SESSION['message'] = "Grade deleted successfully!";
        $_SESSION['message_type'] = "success";
        header('Location: records.php');
        exit();
    } catch (Exception $e) {
        $_SESSION['message'] = "Error deleting grade: " . $e->getMessage();
        $_SESSION['message_type'] = "error";
        header('Location: records.php');
        exit();
    }
} elseif ($action === 'export_records') {
    // Export records to CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="student_records_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');
    fputcsv($output, ['Student ID', 'Student Name', 'Subject Code', 'Subject Name', 'Units', 'Semester', 'School Year', 'Grade', 'Remarks']);

    foreach ($records as $record) {
        fputcsv($output, [
            $record['student_id'],
            $record['first_name'] . ' ' . $record['last_name'],
            $record['subject_code'],
            $record['subject_name'],
            $record['units'],
            getSemesterName($record['semester']),
            $record['school_year'],
            $record['grade'],
            $record['remarks']
        ]);
    }
    fclose($output);
    exit;
} elseif ($action === 'generate_transcript' && isset($_GET['student_id'])) {
    // Generate transcript PDF
    $student_id = $_GET['student_id'];

    // Get student info
    $student_stmt = $conn->prepare("SELECT * FROM users WHERE student_id = ? AND user_type = 'student'");
    $student_stmt->execute([$student_id]);
    $student = $student_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$student) {
        $message = "Student not found!";
        $message_type = "error";
    } else {
        // Get student grades
        $grades_stmt = $conn->prepare("SELECT sg.*, s.subject_code, s.subject_name, s.units
                                      FROM student_grades sg
                                      JOIN subjects s ON sg.subject_id = s.id
                                      WHERE sg.student_id = ?
                                      ORDER BY sg.school_year, sg.semester, s.subject_code");
        $grades_stmt->execute([$student['id']]);
        $grades = $grades_stmt->fetchAll(PDO::FETCH_ASSOC);

        // Generate PDF transcript
        require_once '../includes/transcript_generator.php';
        generateTranscriptPDF($student, $grades);
        exit;
    }
}

// Get students for dropdown
$students_stmt = $conn->prepare("SELECT id, student_id, first_name, last_name FROM users WHERE user_type = 'student' ORDER BY last_name, first_name");
$students_stmt->execute();
$students = $students_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get subjects for dropdown
$subjects_stmt = $conn->prepare("SELECT id, subject_code, subject_name FROM subjects WHERE status = 'active' ORDER BY subject_code");
$subjects_stmt->execute();
$subjects = $subjects_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get student records with search
$search = $_GET['search'] ?? '';
$student_filter = $_GET['student_filter'] ?? '';

$records_query = "SELECT sg.*, u.student_id, u.first_name, u.last_name, s.subject_code, s.subject_name, s.units 
                  FROM student_grades sg 
                  JOIN users u ON sg.student_id = u.id 
                  JOIN subjects s ON sg.subject_id = s.id 
                  WHERE 1=1";

$params = [];
if ($search) {
    $records_query .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.student_id LIKE ? OR s.subject_code LIKE ?)";
    $search_param = "%$search%";
    $params = array_fill(0, 4, $search_param);
}

if ($student_filter) {
    $records_query .= " AND u.id = ?";
    $params[] = $student_filter;
}

$records_query .= " ORDER BY u.last_name, u.first_name, sg.school_year DESC, sg.semester DESC";

$records_stmt = $conn->prepare($records_query);
$records_stmt->execute($params);
$records = $records_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Student Records Management";
?>

<?php include '../includes/admin_layout_start.php'; ?>

<!-- Main Content -->
<div class="flex-1 p-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl shadow-xl text-white p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Student Records</h1>
                    <p class="text-green-100 text-lg">Manage academic records, grades, and transcripts</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-folder-open text-4xl text-white opacity-80"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
            <div class="flex items-center">
                <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Quick Stats -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Students</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo count($students); ?></h3>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-user-graduate text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Records</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo count($records); ?></h3>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-file-alt text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Active Subjects</p>
                    <h3 class="text-2xl font-bold text-gray-800"><?php echo count($subjects); ?></h3>
                </div>
                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-book text-purple-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Passing Rate</p>
                    <h3 class="text-2xl font-bold text-gray-800">85%</h3>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="flex flex-wrap gap-4 mb-6">
        <button onclick="openAddGradeModal()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center">
            <i class="fas fa-plus mr-2"></i>Add Grade
        </button>
        <button onclick="generateTranscript()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <i class="fas fa-file-pdf mr-2"></i>Generate Transcript
        </button>
        <button onclick="exportRecords()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
            <i class="fas fa-download mr-2"></i>Export Records
        </button>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by student name, ID, or subject..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
            </div>
            <div class="min-w-48">
                <select name="student_filter" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    <option value="">All Students</option>
                    <?php foreach ($students as $student): ?>
                        <option value="<?php echo $student['id']; ?>" <?php echo $student_filter == $student['id'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($student['last_name'] . ', ' . $student['first_name'] . ' (' . $student['student_id'] . ')'); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <button type="submit" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            <a href="records.php" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
        </form>
    </div>

    <!-- Records Table -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Academic Records</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if (empty($records)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-folder-open text-2xl text-gray-400"></i>
                    </div>
                    <p class="text-gray-500">No academic records found.</p>
                </div>
            <?php else: ?>
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Student</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Subject</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Units</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Period</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Grade</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Remarks</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($records as $record): ?>
                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($record['last_name'] . ', ' . $record['first_name']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($record['student_id']); ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($record['subject_code']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($record['subject_name']); ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6 text-gray-700"><?php echo $record['units']; ?></td>
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="text-gray-900"><?php echo getSemesterName($record['semester']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo $record['school_year']; ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6">
                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $record['grade'] >= 75 ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                        <?php echo $record['grade']; ?>
                                    </span>
                                </td>
                                <td class="py-4 px-6 text-gray-700"><?php echo htmlspecialchars($record['remarks']); ?></td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button onclick="editGrade(<?php echo $record['id']; ?>)" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors" title="Edit Grade">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button onclick="deleteGrade(<?php echo $record['id']; ?>)" class="bg-red-100 text-red-700 px-3 py-1 rounded-lg text-sm hover:bg-red-200 transition-colors" title="Delete Grade">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Grade Modal -->
<div id="addGradeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-800">Add Grade</h3>
                    <button onclick="closeAddGradeModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <form id="addGradeForm" method="POST" action="?action=add_grade" class="p-6" onsubmit="return handleAddGradeSubmit(this)">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Student</label>
                        <select name="student_id" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">Select Student</option>
                            <?php foreach ($students as $student): ?>
                                <option value="<?php echo $student['id']; ?>">
                                    <?php echo htmlspecialchars($student['last_name'] . ', ' . $student['first_name'] . ' (' . $student['student_id'] . ')'); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Subject</label>
                        <select name="subject_id" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="">Select Subject</option>
                            <?php foreach ($subjects as $subject): ?>
                                <option value="<?php echo $subject['id']; ?>">
                                    <?php echo htmlspecialchars($subject['subject_code'] . ' - ' . $subject['subject_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                            <select name="semester" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                <option value="1">1st Semester</option>
                                <option value="2">2nd Semester</option>
                                <option value="3">Summer</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">School Year</label>
                            <input type="text" name="school_year" value="<?php echo date('Y') . '-' . (date('Y') + 1); ?>" required
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Grade</label>
                        <input type="number" name="grade" min="0" max="100" step="0.01" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Remarks</label>
                        <select name="remarks" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="PASSED">PASSED</option>
                            <option value="FAILED">FAILED</option>
                            <option value="INCOMPLETE">INCOMPLETE</option>
                            <option value="DROPPED">DROPPED</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="closeAddGradeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="addGradeSubmitBtn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        Add Grade
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Grade Modal -->
<div id="editGradeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-800">Edit Grade</h3>
                    <button onclick="closeEditGradeModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <form id="editGradeForm" method="POST" action="?action=edit_grade" class="p-6" onsubmit="return handleEditGradeSubmit(this)">
                <input type="hidden" name="grade_id" id="edit_grade_id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Grade</label>
                        <input type="number" name="grade" id="edit_grade_value" min="0" max="100" step="0.01" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Remarks</label>
                        <select name="remarks" id="edit_remarks" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500">
                            <option value="PASSED">PASSED</option>
                            <option value="FAILED">FAILED</option>
                            <option value="INCOMPLETE">INCOMPLETE</option>
                            <option value="DROPPED">DROPPED</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="closeEditGradeModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="editGradeSubmitBtn" class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700">
                        Update Grade
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Debug function
function debugLog(message) {
    console.log('[Records Debug]:', message);
}

function openAddGradeModal() {
    debugLog('Opening add grade modal');
    const modal = document.getElementById('addGradeModal');
    if (modal) {
        modal.classList.remove('hidden');
    } else {
        console.error('Add grade modal not found');
    }
}

function closeAddGradeModal() {
    debugLog('Closing add grade modal');
    const modal = document.getElementById('addGradeModal');
    if (modal) {
        modal.classList.add('hidden');
    }
    // Reset submit button
    const submitBtn = document.getElementById('addGradeSubmitBtn');
    if (submitBtn) {
        submitBtn.innerHTML = 'Add Grade';
        submitBtn.disabled = false;
    }
}

function handleAddGradeSubmit(form) {
    const submitBtn = document.getElementById('addGradeSubmitBtn');
    const studentId = form.querySelector('select[name="student_id"]').value;
    const subjectId = form.querySelector('select[name="subject_id"]').value;
    const semester = form.querySelector('select[name="semester"]').value;
    const schoolYear = form.querySelector('input[name="school_year"]').value;
    const grade = form.querySelector('input[name="grade"]').value;
    const remarks = form.querySelector('select[name="remarks"]').value;

    // Validate inputs
    if (!studentId) {
        alert('Please select a student');
        return false;
    }

    if (!subjectId) {
        alert('Please select a subject');
        return false;
    }

    if (!semester) {
        alert('Please select a semester');
        return false;
    }

    if (!schoolYear) {
        alert('Please enter school year');
        return false;
    }

    if (!grade || parseFloat(grade) < 0 || parseFloat(grade) > 100) {
        alert('Please enter a valid grade between 0 and 100');
        return false;
    }

    if (!remarks) {
        alert('Please select remarks');
        return false;
    }

    // Show loading state
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding...';
        submitBtn.disabled = true;
    }

    // Allow form submission
    return true;
}

function closeEditGradeModal() {
    debugLog('Closing edit grade modal');
    const modal = document.getElementById('editGradeModal');
    if (modal) {
        modal.classList.add('hidden');
    }
    // Reset submit button
    const submitBtn = document.getElementById('editGradeSubmitBtn');
    if (submitBtn) {
        submitBtn.innerHTML = 'Update Grade';
        submitBtn.disabled = false;
    }
}

function handleEditGradeSubmit(form) {
    const submitBtn = document.getElementById('editGradeSubmitBtn');
    const grade = document.getElementById('edit_grade_value').value;
    const remarks = document.getElementById('edit_remarks').value;

    // Validate inputs
    if (!grade || parseFloat(grade) < 0 || parseFloat(grade) > 100) {
        alert('Please enter a valid grade between 0 and 100');
        return false;
    }

    if (!remarks) {
        alert('Please select remarks');
        return false;
    }

    // Show loading state
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        submitBtn.disabled = true;
    }

    // Allow form submission
    return true;
}

function editGrade(id) {
    // Get grade data via AJAX and populate edit modal
    fetch('get_grade.php?id=' + id)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                document.getElementById('edit_grade_id').value = data.grade.id;
                document.getElementById('edit_grade_value').value = data.grade.grade;
                document.getElementById('edit_remarks').value = data.grade.remarks;
                document.getElementById('editGradeModal').classList.remove('hidden');
            } else {
                alert('Error loading grade data: ' + (data.message || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Error loading grade data. Please try again.');
        });
}

function deleteGrade(id) {
    if (confirm('Are you sure you want to delete this grade record? This action cannot be undone.')) {
        // Show loading state
        const button = event.target.closest('button');
        const originalContent = button.innerHTML;
        button.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        button.disabled = true;

        // Redirect to delete action
        window.location.href = '?action=delete_grade&id=' + id;
    }
}

function generateTranscript() {
    const studentId = prompt('Enter Student ID for transcript generation:');
    if (studentId) {
        window.open('?action=generate_transcript&student_id=' + studentId, '_blank');
    }
}

function exportRecords() {
    window.location.href = '?action=export_records';
}

// Close modal when clicking outside
document.getElementById('addGradeModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddGradeModal();
    }
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
