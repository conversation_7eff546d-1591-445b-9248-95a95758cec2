<?php
// Common functions for Masbate Colleges Online Enrollment System

// Check if database config exists before including
if (file_exists('../config/database.php')) {
    require_once '../config/database.php';
} elseif (file_exists('config/database.php')) {
    require_once 'config/database.php';
} else {
    // Database not configured yet, redirect to installation
    if (!strpos($_SERVER['REQUEST_URI'], 'install.php')) {
        header('Location: install.php');
        exit();
    }
}

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

// Check if user is admin
function isAdmin() {
    return isset($_SESSION['user_type']) && $_SESSION['user_type'] === 'admin';
}

// Redirect if not logged in
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ../login.php');
        exit();
    }
}

// Redirect if not admin
function requireAdmin() {
    requireLogin();
    if (!isAdmin()) {
        header('Location: ../student/dashboard.php');
        exit();
    }
}

// Sanitize input
function sanitizeInput($data) {
    $data = trim($data);
    $data = stripslashes($data);
    $data = htmlspecialchars($data);
    return $data;
}

// Generate student ID
function generateStudentId() {
    $year = date('Y');
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE student_id LIKE ?");
    $pattern = $year . '%';
    $stmt->execute([$pattern]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $count = $result['count'] + 1;
    return $year . str_pad($count, 4, '0', STR_PAD_LEFT);
}

// Upload file function
function uploadFile($file, $uploadDir, $allowedTypes = ['jpg', 'jpeg', 'png', 'pdf']) {
    if (!isset($file['error']) || is_array($file['error'])) {
        return ['success' => false, 'message' => 'Invalid file upload'];
    }

    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            break;
        case UPLOAD_ERR_NO_FILE:
            return ['success' => false, 'message' => 'No file sent'];
        case UPLOAD_ERR_INI_SIZE:
        case UPLOAD_ERR_FORM_SIZE:
            return ['success' => false, 'message' => 'File size exceeded'];
        default:
            return ['success' => false, 'message' => 'Unknown upload error'];
    }

    if ($file['size'] > 5000000) { // 5MB limit
        return ['success' => false, 'message' => 'File size too large (max 5MB)'];
    }

    $finfo = new finfo(FILEINFO_MIME_TYPE);
    $mimeType = $finfo->file($file['tmp_name']);
    
    $allowedMimes = [
        'jpg' => 'image/jpeg',
        'jpeg' => 'image/jpeg',
        'png' => 'image/png',
        'pdf' => 'application/pdf'
    ];

    $ext = array_search($mimeType, $allowedMimes, true);
    if ($ext === false || !in_array($ext, $allowedTypes)) {
        return ['success' => false, 'message' => 'Invalid file type'];
    }

    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }

    $filename = sprintf('%s_%s.%s', uniqid(), time(), $ext);
    $filepath = $uploadDir . '/' . $filename;

    if (!move_uploaded_file($file['tmp_name'], $filepath)) {
        return ['success' => false, 'message' => 'Failed to move uploaded file'];
    }

    return ['success' => true, 'filename' => $filename, 'filepath' => $filepath];
}

// Get current semester and school year
function getCurrentSemesterYear() {
    $month = date('n');
    $year = date('Y');
    
    if ($month >= 6 && $month <= 10) {
        // First semester (June-October)
        $semester = 1;
        $school_year = $year . '-' . ($year + 1);
    } elseif ($month >= 11 || $month <= 3) {
        // Second semester (November-March)
        $semester = 2;
        if ($month >= 11) {
            $school_year = $year . '-' . ($year + 1);
        } else {
            $school_year = ($year - 1) . '-' . $year;
        }
    } else {
        // Summer semester (April-May)
        $semester = 3;
        $school_year = ($year - 1) . '-' . $year;
    }
    
    return ['semester' => $semester, 'school_year' => $school_year];
}

// Calculate fees based on units
function calculateFees($units) {
    $rate_per_unit = 500; // PHP 500 per unit
    $miscellaneous_fee = 2000; // Fixed miscellaneous fee
    return ($units * $rate_per_unit) + $miscellaneous_fee;
}

// Format currency
function formatCurrency($amount) {
    return '₱' . number_format($amount, 2);
}

// Get semester name
function getSemesterName($semester) {
    switch ($semester) {
        case 1: return '1st Semester';
        case 2: return '2nd Semester';
        case 3: return 'Summer';
        default: return 'Unknown';
    }
}

// Log activity
function logActivity($user_id, $action, $details = '') {
    $db = new Database();
    $conn = $db->getConnection();
    
    $stmt = $conn->prepare("INSERT INTO activity_logs (user_id, action, details, created_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$user_id, $action, $details]);
}

// Send notification (placeholder for future email integration)
function sendNotification($user_id, $subject, $message) {
    // This can be extended to send actual emails
    $db = new Database();
    $conn = $db->getConnection();

    $stmt = $conn->prepare("INSERT INTO notifications (user_id, subject, message, created_at) VALUES (?, ?, ?, NOW())");
    $stmt->execute([$user_id, $subject, $message]);
}

// Get ordinal suffix for numbers (1st, 2nd, 3rd, 4th, etc.)
function getOrdinalSuffix($number) {
    $ends = array('th','st','nd','rd','th','th','th','th','th','th');
    if ((($number % 100) >= 11) && (($number % 100) <= 13))
        return 'th';
    else
        return $ends[$number % 10];
}

// Get payment settings from database
function getPaymentSettings() {
    $default_settings = [
        'gcash_number' => '09XX-XXX-XXXX',
        'gcash_name' => 'Masbate Colleges',
        'bank_name' => 'BPI',
        'bank_account_number' => 'XXXXXXXXXX',
        'bank_account_name' => 'Masbate Colleges',
        'otc_location' => 'Registrar\'s Office',
        'otc_hours' => 'Monday-Friday, 8:00 AM - 5:00 PM',
        'payment_instructions' => 'Please ensure payment amount matches the total fees exactly.'
    ];

    try {
        $db = new Database();
        $conn = $db->getConnection();

        $stmt = $conn->prepare("SELECT setting_key, setting_value FROM system_settings WHERE setting_key IN (?, ?, ?, ?, ?, ?, ?, ?)");
        $stmt->execute([
            'gcash_number', 'gcash_name', 'bank_name', 'bank_account_number',
            'bank_account_name', 'otc_location', 'otc_hours', 'payment_instructions'
        ]);
        $db_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($db_settings as $setting) {
            $default_settings[$setting['setting_key']] = $setting['setting_value'];
        }
    } catch (Exception $e) {
        // Return defaults if database query fails
        error_log("Failed to get payment settings: " . $e->getMessage());
    }

    return $default_settings;
}
?>
