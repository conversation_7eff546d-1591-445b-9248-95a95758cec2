# Masbate Colleges Online Enrollment System

A comprehensive web-based enrollment system designed for Masbate Colleges, allowing students to enroll online and administrators to manage the enrollment process efficiently.

## 🎯 Features

### 👨‍🎓 Student Features
- **User Registration & Login** - Secure account creation and authentication
- **Online Enrollment** - Select subjects and view computed fees
- **Payment Upload** - Upload proof of payment (GCash, Bank Transfer, Over-the-Counter)
- **Real-time Status Tracking** - Monitor enrollment approval status
- **COR Download** - Download Certificate of Registration after approval
- **Enrollment History** - View past enrollment records

### 👨‍💼 Admin/Registrar Features
- **Dashboard Overview** - Comprehensive system statistics
- **Enrollment Management** - Review and approve/return enrollments
- **Student Management** - View and manage student accounts
- **Subject Management** - Add and manage course offerings
- **Payment Verification** - Review uploaded payment proofs
- **Report Generation** - Export enrollment data (PDF/Excel)
- **Bulk Operations** - Approve multiple enrollments at once

### 📋 Certificate of Registration (COR)
- **Auto-generation** - Automatically created upon enrollment approval
- **Professional Format** - Includes all required information
- **Download/Print** - Available in PDF format
- **Official Documentation** - School logo and signature sections

## 🛠️ Technology Stack

- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Backend**: PHP 7.4+
- **Database**: MySQL/MariaDB
- **Icons**: Font Awesome 6
- **Styling**: Custom CSS with responsive design

## 📋 Requirements

- **Web Server**: Apache/Nginx with PHP support
- **PHP**: Version 7.4 or higher
- **Database**: MySQL 5.7+ or MariaDB 10.2+
- **Extensions**: PDO MySQL, GD (for image handling)
- **Storage**: Minimum 100MB for uploads

## 🚀 Installation

### Method 1: Using the Installation Script

1. **Download/Clone** the project files to your web server directory
2. **Navigate** to your project URL in a web browser
3. **Run** the installation script: `http://yoursite.com/install.php`
4. **Follow** the installation wizard
5. **Complete** the setup and start using the system

### Method 2: Manual Installation

1. **Extract** files to your web server directory (e.g., `htdocs/enrollment`)
2. **Create** a MySQL database named `masbate_enrollment`
3. **Configure** database settings in `config/database.php`
4. **Set permissions** for the `uploads` directory (755)
5. **Access** the system via your web browser

## 🔐 Default Login Credentials

### Administrator
- **Email**: <EMAIL>
- **Password**: admin123

### Sample Student
- **Email**: <EMAIL>
- **Password**: student123

> ⚠️ **Important**: Change these default passwords immediately after installation!

## 📁 Directory Structure

```
enrollment-system/
├── admin/                  # Admin panel files
├── assets/                 # CSS, JS, and static assets
├── config/                 # Database configuration
├── includes/               # Common PHP functions and headers
├── student/                # Student portal files
├── uploads/                # File upload directory
│   └── payments/          # Payment proof uploads
├── index.php              # Homepage
├── login.php              # Login page
├── register.php           # Student registration
├── install.php            # Installation script
└── README.md              # This file
```

## 🔧 Configuration

### Database Settings
Edit `config/database.php` to match your database configuration:

```php
private $host = "localhost";
private $db_name = "nsc_enrollment";
private $username = "root";
private $password = "";
```

### File Upload Settings
- Maximum file size: 5MB
- Allowed formats: JPG, PNG, PDF
- Upload directory: `uploads/payments/`

### Security Features
- Password hashing using PHP's `password_hash()`
- SQL injection prevention with prepared statements
- File upload validation and restrictions
- Session management and CSRF protection

## 🎨 Customization

### Branding
- Update school information in `includes/header.php` and `includes/footer.php`
- Modify colors in `assets/css/style.css` (CSS variables)
- Replace logo and school details in COR template

### Fee Structure
- Modify fee calculation in `includes/functions.php`
- Update rate per unit and miscellaneous fees

### Email Integration
- Extend notification functions in `includes/functions.php`
- Add SMTP configuration for email notifications

## 📊 System Workflow

### Enrollment Process
1. **Student Registration** - Create account with course and year level
2. **Subject Selection** - Choose available subjects for the semester
3. **Fee Calculation** - System automatically computes total fees
4. **Payment Upload** - Submit proof of payment
5. **Admin Review** - Registrar reviews and approves enrollment
6. **COR Generation** - Certificate of Registration becomes available

### Admin Workflow
1. **Dashboard Review** - Monitor pending enrollments
2. **Payment Verification** - Check uploaded payment proofs
3. **Enrollment Approval** - Approve or return enrollments with remarks
4. **Report Generation** - Export enrollment data for records

## 🔒 Security Considerations

- Change default passwords immediately
- Use HTTPS in production
- Regular database backups
- Keep PHP and dependencies updated
- Monitor file uploads for security

## 🐛 Troubleshooting

### Common Issues

**Database Connection Error**
- Check MySQL service is running
- Verify database credentials in `config/database.php`
- Ensure database exists

**File Upload Issues**
- Check `uploads/` directory permissions (755)
- Verify PHP `file_uploads` is enabled
- Check `upload_max_filesize` in php.ini

**Session Issues**
- Ensure session directory is writable
- Check PHP session configuration

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: (*************

## 📄 License

This project is developed for Masbate Colleges. All rights reserved.

## 🤝 Contributing

This is a custom system for Masbate Colleges. For modifications or enhancements, please contact the development team.

---

**Masbate Colleges Online Enrollment System v1.0**
*Empowering Education Through Technology*
