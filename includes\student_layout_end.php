        </div>
    </div>

    <!-- Student Footer -->
    <footer class="bg-white border-t border-gray-200 mt-auto">
        <div class="px-6 py-4">
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-600">
                    © <?php echo date('Y'); ?> Masbate Colleges. All rights reserved.
                </div>
                <div class="flex items-center space-x-4 text-sm text-gray-600">
                    <span>Student Portal v1.0</span>
                    <span>•</span>
                    <span>Enrollment System</span>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script>
        // Add smooth scrolling
        document.documentElement.style.scrollBehavior = 'smooth';
        
        // Add loading states for buttons
        document.addEventListener('DOMContentLoaded', function() {
            const buttons = document.querySelectorAll('button[type="submit"], .btn-submit');
            buttons.forEach(button => {
                button.addEventListener('click', function() {
                    if (this.form && this.form.checkValidity()) {
                        this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                        this.disabled = true;
                    }
                });
            });
        });

        // Add fade-in animation to cards
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);

        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.card-hover, .bg-white');
            cards.forEach(card => observer.observe(card));
        });

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            // Only target actual alert messages, not status badges or buttons
            const alerts = document.querySelectorAll('.alert, [role="alert"], .alert-message');
            alerts.forEach(alert => {
                // Additional check to ensure we're only targeting alert elements
                if (alert.classList.contains('alert') ||
                    alert.getAttribute('role') === 'alert' ||
                    alert.classList.contains('alert-message')) {
                    setTimeout(() => {
                        alert.style.transition = 'opacity 0.5s ease';
                        alert.style.opacity = '0';
                        setTimeout(() => alert.remove(), 500);
                    }, 5000);
                }
            });
        });

        // Form validation enhancements
        document.addEventListener('DOMContentLoaded', function() {
            const inputs = document.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', function() {
                    if (this.hasAttribute('required') && !this.value.trim()) {
                        this.classList.add('border-red-500', 'bg-red-50');
                        this.classList.remove('border-gray-300');
                    } else {
                        this.classList.remove('border-red-500', 'bg-red-50');
                        this.classList.add('border-gray-300');
                    }
                });
                
                input.addEventListener('input', function() {
                    if (this.value.trim()) {
                        this.classList.remove('border-red-500', 'bg-red-50');
                        this.classList.add('border-gray-300');
                    }
                });
            });
        });
    </script>
</body>
</html>
