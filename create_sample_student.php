<?php
// <PERSON><PERSON><PERSON> to manually create sample student for testing

try {
    $pdo = new PDO("mysql:host=localhost;dbname=masbate_enrollment", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Creating Sample Student...</h2>";
    
    // Check if student already exists
    $check_stmt = $pdo->prepare("SELECT * FROM users WHERE email = ? OR student_id = ?");
    $check_stmt->execute(['<EMAIL>', '20240001']);
    $existing = $check_stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($existing) {
        echo "<p style='color: orange;'>Student already exists:</p>";
        echo "<pre>" . print_r($existing, true) . "</pre>";
        
        // Delete existing student for fresh creation
        $delete_stmt = $pdo->prepare("DELETE FROM users WHERE email = ? OR student_id = ?");
        $delete_stmt->execute(['<EMAIL>', '20240001']);
        echo "<p style='color: red;'>Deleted existing student for fresh creation.</p>";
    }
    
    // Create new student
    $student_password = password_hash('student123', PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("INSERT INTO users (student_id, email, password, first_name, last_name, middle_name, contact_number, address, course_id, year_level, user_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    
    $result = $stmt->execute([
        '20240001', 
        '<EMAIL>',
        $student_password, 
        'Juan', 
        'Dela Cruz', 
        'Santos', 
        '09123456789', 
        'Sample Address, Masbate City, Masbate',
        1, 
        1, 
        'student'
    ]);
    
    if ($result) {
        echo "<p style='color: green;'>✓ Sample student created successfully!</p>";
        
        // Verify creation
        $verify_stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");
        $verify_stmt->execute(['<EMAIL>']);
        $created_student = $verify_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($created_student) {
            echo "<h3>Created Student Details:</h3>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            foreach ($created_student as $key => $value) {
                if ($key !== 'password') { // Don't show password hash
                    echo "<tr><td><strong>$key</strong></td><td>" . htmlspecialchars($value) . "</td></tr>";
                }
            }
            echo "</table>";
            
            echo "<h3>Login Test:</h3>";
            echo "<p>Email: <EMAIL></p>";
            echo "<p>Password: student123</p>";
            echo "<p>Password Hash Verification: " . (password_verify('student123', $created_student['password']) ? '✓ Valid' : '✗ Invalid') . "</p>";
        }
    } else {
        echo "<p style='color: red;'>✗ Failed to create student</p>";
        echo "<p>Error Info: " . print_r($stmt->errorInfo(), true) . "</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>

<a href="login.php">Test Login</a> | 
<a href="check_database.php">Check Database</a> | 
<a href="install.php">Installation</a>
