<?php
// Most basic test possible
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Basic Test</h1>";

// Test 1: P<PERSON> is working
echo "<p>✅ PHP is working - Current time: " . date('Y-m-d H:i:s') . "</p>";

// Test 2: Session
session_start();
echo "<p>✅ Session started - Session ID: " . session_id() . "</p>";

// Test 3: POST handling
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    echo "<div style='background: #dcfce7; padding: 10px; margin: 10px 0; border: 1px solid #16a34a;'>";
    echo "<h3>✅ POST Request Received!</h3>";
    echo "<p>POST Data: " . print_r($_POST, true) . "</p>";
    echo "<p>This proves POST requests are working.</p>";
    echo "</div>";
    
    // Test redirect
    if (isset($_POST['test_redirect'])) {
        header('Location: basic_test.php?redirected=1');
        exit();
    }
}

// Test 4: GET handling
if (isset($_GET['redirected'])) {
    echo "<div style='background: #fef3c7; padding: 10px; margin: 10px 0; border: 1px solid #f59e0b;'>";
    echo "<h3>✅ Redirect Test Successful!</h3>";
    echo "<p>You were redirected successfully.</p>";
    echo "</div>";
}

// Test 5: Database connection
try {
    $pdo = new PDO("mysql:host=localhost;dbname=masbate_enrollment", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    echo "<p>✅ Database connection successful</p>";
    
    // Test query
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
    $count = $stmt->fetch()['count'];
    echo "<p>✅ Database query successful - Users count: $count</p>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ Database error: " . $e->getMessage() . "</p>";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Basic Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-form { background: #f9f9f9; padding: 20px; margin: 20px 0; border: 1px solid #ddd; }
        button { background: #16a34a; color: white; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #15803d; }
    </style>
</head>
<body>
    <div class="test-form">
        <h3>Test Form Submission</h3>
        <form method="POST" action="basic_test.php">
            <input type="hidden" name="test_action" value="basic_test">
            <button type="submit">Test POST Request</button>
        </form>
    </div>

    <div class="test-form">
        <h3>Test Redirect</h3>
        <form method="POST" action="basic_test.php">
            <input type="hidden" name="test_redirect" value="1">
            <button type="submit">Test Redirect</button>
        </form>
    </div>

    <div class="test-form">
        <h3>Test GET Request</h3>
        <a href="basic_test.php?test_get=1" style="background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Test GET</a>
        <?php if (isset($_GET['test_get'])): ?>
            <p style="color: green;">✅ GET request received!</p>
        <?php endif; ?>
    </div>

    <div class="test-form">
        <h3>Navigation Links</h3>
        <a href="records.php" style="background: #16a34a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">Records</a>
        <a href="registrar.php" style="background: #7c3aed; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">Registrar</a>
        <a href="accounting.php" style="background: #059669; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;">Accounting</a>
    </div>

    <script>
        console.log('Basic test page loaded');
        
        // Test JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function() {
                    console.log('Form submitted');
                    const btn = this.querySelector('button[type="submit"]');
                    if (btn) {
                        btn.innerHTML = 'Processing...';
                        btn.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>
