<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// For testing, simulate admin login
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'admin';
    $_SESSION['first_name'] = 'Test';
    $_SESSION['last_name'] = 'Admin';
}

$db = new Database();
$conn = $db->getConnection();

$message = $_SESSION['message'] ?? '';
$message_type = $_SESSION['message_type'] ?? '';

// Clear session messages
if (isset($_SESSION['message'])) {
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_action'])) {
    if ($_POST['test_action'] === 'test_grade') {
        try {
            // Simple test insertion
            $stmt = $conn->prepare("INSERT INTO student_grades (student_id, subject_id, semester, school_year, grade, remarks, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
            $result = $stmt->execute([1, 1, 1, '2024-2025', 88.5, 'PASSED']);
            
            if ($result) {
                $_SESSION['message'] = "Test grade added successfully!";
                $_SESSION['message_type'] = "success";
                
                // Clean up test data
                $conn->query("DELETE FROM student_grades WHERE grade = 88.5 AND school_year = '2024-2025'");
            } else {
                $_SESSION['message'] = "Test grade insertion failed!";
                $_SESSION['message_type'] = "error";
            }
        } catch (Exception $e) {
            $_SESSION['message'] = "Error: " . $e->getMessage();
            $_SESSION['message_type'] = "error";
        }
        
        header('Location: test_simple_form.php');
        exit();
    }
}

$page_title = "Simple Form Test";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen p-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl shadow-xl text-white p-8 mb-8">
                <h1 class="text-3xl font-bold mb-2">Simple Form Test</h1>
                <p class="text-blue-100 text-lg">Testing form submission without layout interference</p>
            </div>

            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                    <div class="flex items-center">
                        <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Simple Test Form -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Test Form Submission</h2>
                
                <form method="POST" action="test_simple_form.php" class="space-y-4">
                    <input type="hidden" name="test_action" value="test_grade">
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Test Action</label>
                        <p class="text-gray-600">This will test inserting and immediately deleting a test grade record.</p>
                    </div>
                    
                    <div class="flex space-x-4">
                        <button type="submit" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-play mr-2"></i>Run Test
                        </button>
                        <a href="records.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-folder-open mr-2"></i>Go to Records
                        </a>
                        <a href="debug_actions.php" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                            <i class="fas fa-bug mr-2"></i>Debug Actions
                        </a>
                    </div>
                </form>
            </div>

            <!-- Database Status -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Database Status</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <?php
                    $tables = ['users', 'student_grades', 'enrollments', 'payments'];
                    foreach ($tables as $table) {
                        try {
                            $result = $conn->query("SELECT COUNT(*) as count FROM $table");
                            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
                            echo "<div class='text-center p-3 bg-green-50 rounded-lg'>";
                            echo "<div class='text-2xl font-bold text-green-600'>$count</div>";
                            echo "<div class='text-sm text-gray-600'>$table</div>";
                            echo "</div>";
                        } catch (Exception $e) {
                            echo "<div class='text-center p-3 bg-red-50 rounded-lg'>";
                            echo "<div class='text-2xl font-bold text-red-600'>Error</div>";
                            echo "<div class='text-sm text-gray-600'>$table</div>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Simple form handling without interference
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Simple form test page loaded');
            
            // Test if JavaScript is working
            const forms = document.querySelectorAll('form');
            console.log('Found', forms.length, 'forms');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    console.log('Form submitted:', this);
                    const submitBtn = this.querySelector('button[type="submit"]');
                    if (submitBtn) {
                        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Testing...';
                        submitBtn.disabled = true;
                    }
                });
            });
        });
    </script>
</body>
</html>
