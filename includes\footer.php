    </div> <!-- End main content -->

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-12 mt-auto">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-12 h-12 rounded-lg overflow-hidden">
                            <img src="<?php echo isset($base_url) ? $base_url : ''; ?>/assets/logo-school.png" alt="Masbate Colleges Logo" class="w-full h-full object-cover">
                        </div>
                        <div>
                            <h3 class="text-2xl font-bold">Masbate Colleges</h3>
                            <p class="text-gray-400">Online Enrollment System</p>
                        </div>
                    </div>
                    <p class="text-gray-300 leading-relaxed max-w-md">
                        Empowering education through technology. Our online enrollment system makes course registration
                        simple, secure, and accessible for all students.
                    </p>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2">
                        <li><a href="<?php echo isset($base_url) ? $base_url : ''; ?>/index.php" class="text-gray-300 hover:text-white transition-colors">Home</a></li>
                        <li><a href="<?php echo isset($base_url) ? $base_url : ''; ?>/login.php" class="text-gray-300 hover:text-white transition-colors">Login</a></li>
                        <li><a href="<?php echo isset($base_url) ? $base_url : ''; ?>/register.php" class="text-gray-300 hover:text-white transition-colors">Register</a></li>
                        <?php if (isset($_SESSION['user_id'])): ?>
                            <?php if ($_SESSION['user_type'] === 'admin'): ?>
                                <li><a href="<?php echo isset($base_url) ? $base_url : ''; ?>/admin/dashboard.php" class="text-gray-300 hover:text-white transition-colors">Dashboard</a></li>
                            <?php else: ?>
                                <li><a href="<?php echo isset($base_url) ? $base_url : ''; ?>/student/dashboard.php" class="text-gray-300 hover:text-white transition-colors">Dashboard</a></li>
                            <?php endif; ?>
                        <?php endif; ?>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-semibold mb-4">Contact Info</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center">
                            <i class="fas fa-map-marker-alt text-nsc-secondary mr-3"></i>
                            <span class="text-gray-300">Masbate City, Masbate, Philippines</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-phone text-nsc-secondary mr-3"></i>
                            <span class="text-gray-300">(*************</span>
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-envelope text-nsc-secondary mr-3"></i>
                            <span class="text-gray-300"><EMAIL></span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-8 pt-8 text-center">
                <p class="text-gray-400">
                    &copy; <?php echo date('Y'); ?> Masbate Colleges. All rights reserved.
                    Online Enrollment System v2.0 - Powered by modern web technology.
                </p>
            </div>
        </div>
    </footer>

    <!-- Modern JavaScript -->
    <script>
        // Global JavaScript functions for modern UI

        // Show loading spinner
        function showLoading(button) {
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner spinner mr-2"></i>Loading...';
            button.disabled = true;
            button.classList.add('loading');
            button.setAttribute('data-original-text', originalText);
        }

        // Hide loading spinner
        function hideLoading(button) {
            const originalText = button.getAttribute('data-original-text');
            button.innerHTML = originalText;
            button.disabled = false;
            button.classList.remove('loading');
        }

        // Show modern alert message
        function showAlert(message, type = 'info') {
            const alertTypes = {
                'success': 'bg-green-100 border-green-400 text-green-700',
                'error': 'bg-red-100 border-red-400 text-red-700',
                'warning': 'bg-yellow-100 border-yellow-400 text-yellow-700',
                'info': 'bg-blue-100 border-blue-400 text-blue-700'
            };

            const alertClass = alertTypes[type] || alertTypes['info'];
            const iconMap = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'warning': 'fas fa-exclamation-triangle',
                'info': 'fas fa-info-circle'
            };

            const alertHtml = `
                <div class="alert-modern ${alertClass} border-l-4 p-4 mb-4 rounded-r-lg animate-fade-in" role="alert">
                    <div class="flex items-center">
                        <i class="${iconMap[type]} mr-3"></i>
                        <span>${message}</span>
                        <button type="button" class="ml-auto text-lg font-bold hover:opacity-70" onclick="this.parentElement.parentElement.remove()">
                            &times;
                        </button>
                    </div>
                </div>
            `;

            // Insert at the top of main content
            const mainContent = document.querySelector('.pt-16') || document.querySelector('main') || document.body;
            if (mainContent) {
                mainContent.insertAdjacentHTML('afterbegin', alertHtml);
            }
        }

        // Modern confirm dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }

        // Format currency
        function formatCurrency(amount) {
            return '₱' + parseFloat(amount).toLocaleString('en-PH', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                const alerts = document.querySelectorAll('.alert-modern:not(.alert-permanent)');
                alerts.forEach(function(alert) {
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateY(-10px)';
                    setTimeout(() => alert.remove(), 300);
                });
            }, 5000);
        });

        // File upload preview
        function previewFile(input, previewId) {
            const file = input.files[0];
            const preview = document.getElementById(previewId);

            if (file) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    if (file.type.startsWith('image/')) {
                        preview.innerHTML = `<img src="${e.target.result}" class="max-h-48 w-auto rounded-lg shadow-md">`;
                    } else {
                        preview.innerHTML = `
                            <div class="text-center p-6 border-2 border-dashed border-gray-300 rounded-lg">
                                <i class="fas fa-file-pdf text-6xl text-red-500 mb-4"></i>
                                <p class="text-gray-700 font-medium">${file.name}</p>
                            </div>
                        `;
                    }
                };
                reader.readAsDataURL(file);
            } else {
                preview.innerHTML = '';
            }
        }

        // Form validation
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            inputs.forEach(function(input) {
                if (!input.value.trim()) {
                    input.classList.add('border-red-500', 'bg-red-50');
                    input.classList.remove('border-gray-300');
                    isValid = false;
                } else {
                    input.classList.remove('border-red-500', 'bg-red-50');
                    input.classList.add('border-gray-300');
                }
            });

            return isValid;
        }

        // Auto-calculate fees
        function calculateTotalFees() {
            const checkboxes = document.querySelectorAll('input[name="subjects[]"]:checked');
            let totalUnits = 0;

            checkboxes.forEach(function(checkbox) {
                totalUnits += parseInt(checkbox.getAttribute('data-units') || 0);
            });

            const ratePerUnit = 500;
            const miscFee = 2000;
            const totalFees = (totalUnits * ratePerUnit) + miscFee;

            // Update display
            const unitsDisplay = document.getElementById('total-units');
            const feesDisplay = document.getElementById('total-fees');

            if (unitsDisplay) unitsDisplay.textContent = totalUnits;
            if (feesDisplay) feesDisplay.textContent = formatCurrency(totalFees);
        }

        // Smooth scroll for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });

        // Add loading states to forms
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('form');
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const submitBtn = form.querySelector('button[type="submit"]');
                    if (submitBtn && validateForm(form.id)) {
                        showLoading(submitBtn);
                    }
                });
            });
        });
    </script>
    
    <?php if (isset($additional_js)): ?>
        <?php echo $additional_js; ?>
    <?php endif; ?>
</body>
</html>
