<?php
session_start();
require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        header('Location: admin/dashboard.php');
    } else {
        header('Location: student/dashboard.php');
    }
    exit();
}

$error_message = '';
$success_message = '';

// Get courses for dropdown (ensure unique courses)
$db = new Database();
$conn = $db->getConnection();
$stmt = $conn->prepare("SELECT * FROM courses WHERE status = 'active' GROUP BY course_code, course_name ORDER BY course_name");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $first_name = sanitizeInput($_POST['first_name']);
    $last_name = sanitizeInput($_POST['last_name']);
    $middle_name = sanitizeInput($_POST['middle_name']);
    $email = sanitizeInput($_POST['email']);
    $contact_number = sanitizeInput($_POST['contact_number']);
    $address = sanitizeInput($_POST['address']);
    $course_id = (int)$_POST['course_id'];
    $year_level = (int)$_POST['year_level'];
    $password = $_POST['password'];
    $confirm_password = $_POST['confirm_password'];
    
    // Validation
    if (empty($first_name) || empty($last_name) || empty($email) || empty($password) || empty($confirm_password) || empty($course_id) || empty($year_level)) {
        $error_message = 'Please fill in all required fields.';
    } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $error_message = 'Please enter a valid email address.';
    } elseif (strlen($password) < 6) {
        $error_message = 'Password must be at least 6 characters long.';
    } elseif ($password !== $confirm_password) {
        $error_message = 'Passwords do not match.';
    } else {
        // Check if email already exists
        $stmt = $conn->prepare("SELECT id FROM users WHERE email = ?");
        $stmt->execute([$email]);
        if ($stmt->fetch()) {
            $error_message = 'Email address is already registered.';
        } else {
            // Generate student ID
            $student_id = generateStudentId();
            
            // Hash password
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            
            // Insert new user
            $stmt = $conn->prepare("INSERT INTO users (student_id, email, password, first_name, last_name, middle_name, contact_number, address, course_id, year_level, user_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 'student')");
            
            if ($stmt->execute([$student_id, $email, $hashed_password, $first_name, $last_name, $middle_name, $contact_number, $address, $course_id, $year_level])) {
                $success_message = "Registration successful! Your Student ID is: <strong>$student_id</strong>. You can now login.";
                
                // Log activity
                $user_id = $conn->lastInsertId();
                logActivity($user_id, 'register', 'New user registered');
            } else {
                $error_message = 'Registration failed. Please try again.';
            }
        }
    }
}

$page_title = "Register";
?>

<?php include 'includes/header.php'; ?>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-2xl mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 p-3 shadow-lg">
                <img src="assets/logo-school.png" alt="Masbate Colleges Logo" class="w-full h-full object-cover rounded-full">
            </div>
            <h2 class="text-3xl font-bold text-gray-900">Create Your Account</h2>
            <p class="text-gray-600 mt-2">Join Masbate Colleges Online Enrollment System</p>
        </div>

        <!-- Registration Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8 card-hover">
            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>
                    
            <form method="POST" action="" id="registerForm" class="space-y-6">
                <!-- Name Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-nsc-primary"></i>First Name *
                        </label>
                        <input type="text"
                               id="first_name"
                               name="first_name"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                               placeholder="Enter your first name"
                               value="<?php echo isset($_POST['first_name']) ? htmlspecialchars($_POST['first_name']) : ''; ?>">
                    </div>
                    <div>
                        <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-user mr-2 text-nsc-primary"></i>Last Name *
                        </label>
                        <input type="text"
                               id="last_name"
                               name="last_name"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                               placeholder="Enter your last name"
                               value="<?php echo isset($_POST['last_name']) ? htmlspecialchars($_POST['last_name']) : ''; ?>">
                    </div>
                </div>

                <!-- Middle Name -->
                <div>
                    <label for="middle_name" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-nsc-primary"></i>Middle Name
                    </label>
                    <input type="text"
                           id="middle_name"
                           name="middle_name"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                           placeholder="Enter your middle name (optional)"
                           value="<?php echo isset($_POST['middle_name']) ? htmlspecialchars($_POST['middle_name']) : ''; ?>">
                </div>

                <!-- Email -->
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-envelope mr-2 text-nsc-primary"></i>Email Address *
                    </label>
                    <input type="email"
                           id="email"
                           name="email"
                           required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                           placeholder="Enter your email address"
                           value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>">
                </div>

                <!-- Contact Number -->
                <div>
                    <label for="contact_number" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-phone mr-2 text-nsc-primary"></i>Contact Number
                    </label>
                    <input type="tel"
                           id="contact_number"
                           name="contact_number"
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                           placeholder="Enter your contact number"
                           value="<?php echo isset($_POST['contact_number']) ? htmlspecialchars($_POST['contact_number']) : ''; ?>">
                </div>

                <!-- Address -->
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-map-marker-alt mr-2 text-nsc-primary"></i>Address
                    </label>
                    <textarea id="address"
                              name="address"
                              rows="3"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                              placeholder="Enter your complete address"><?php echo isset($_POST['address']) ? htmlspecialchars($_POST['address']) : ''; ?></textarea>
                </div>
                        
                <!-- Course and Year Level -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="md:col-span-2">
                        <label for="course_id" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-graduation-cap mr-2 text-nsc-primary"></i>Course *
                        </label>
                        <select id="course_id"
                                name="course_id"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                            <option value="">Select Course</option>
                            <?php foreach ($courses as $course): ?>
                                <option value="<?php echo $course['id']; ?>"
                                        <?php echo (isset($_POST['course_id']) && $_POST['course_id'] == $course['id']) ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="year_level" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-calendar mr-2 text-nsc-primary"></i>Year Level *
                        </label>
                        <select id="year_level"
                                name="year_level"
                                required
                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                            <option value="">Select Year</option>
                            <option value="1" <?php echo (isset($_POST['year_level']) && $_POST['year_level'] == '1') ? 'selected' : ''; ?>>1st Year</option>
                            <option value="2" <?php echo (isset($_POST['year_level']) && $_POST['year_level'] == '2') ? 'selected' : ''; ?>>2nd Year</option>
                            <option value="3" <?php echo (isset($_POST['year_level']) && $_POST['year_level'] == '3') ? 'selected' : ''; ?>>3rd Year</option>
                            <option value="4" <?php echo (isset($_POST['year_level']) && $_POST['year_level'] == '4') ? 'selected' : ''; ?>>4th Year</option>
                        </select>
                    </div>
                </div>
                        
                <!-- Password Fields -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-nsc-primary"></i>Password *
                        </label>
                        <div class="relative">
                            <input type="password"
                                   id="password"
                                   name="password"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors pr-12"
                                   placeholder="Enter your password">
                            <button type="button"
                                    id="togglePassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Minimum 6 characters</p>
                    </div>
                    <div>
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                            <i class="fas fa-lock mr-2 text-nsc-primary"></i>Confirm Password *
                        </label>
                        <div class="relative">
                            <input type="password"
                                   id="confirm_password"
                                   name="confirm_password"
                                   required
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors pr-12"
                                   placeholder="Confirm your password">
                            <button type="button"
                                    id="toggleConfirmPassword"
                                    class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="flex items-start">
                    <input type="checkbox"
                           id="terms"
                           name="terms"
                           required
                           class="h-4 w-4 text-nsc-primary focus:ring-nsc-primary border-gray-300 rounded mt-1">
                    <label for="terms" class="ml-3 block text-sm text-gray-700">
                        I agree to the <a href="#" class="text-nsc-primary hover:text-nsc-secondary transition-colors underline">Terms and Conditions</a> *
                    </label>
                </div>

                <!-- Submit Button -->
                <button type="submit"
                        class="w-full bg-gradient-to-r from-nsc-primary to-nsc-secondary text-white py-3 px-4 rounded-lg font-semibold hover:from-nsc-secondary hover:to-nsc-primary transition-all duration-300 transform hover:scale-105 btn-hover">
                    <i class="fas fa-user-plus mr-2"></i>Create Account
                </button>
            </form>
                    
            <!-- Divider -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-gray-600 mb-4">Already have an account?</p>
                    <a href="login.php"
                       class="inline-flex items-center px-6 py-3 border border-nsc-primary text-nsc-primary rounded-lg hover:bg-nsc-primary hover:text-white transition-all duration-300">
                        <i class="fas fa-sign-in-alt mr-2"></i>Login Here
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');
    const toggleConfirmPassword = document.getElementById('toggleConfirmPassword');
    const confirmPasswordField = document.getElementById('confirm_password');

    if (togglePassword && passwordField) {
        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }

    if (toggleConfirmPassword && confirmPasswordField) {
        toggleConfirmPassword.addEventListener('click', function() {
            const type = confirmPasswordField.getAttribute('type') === 'password' ? 'text' : 'password';
            confirmPasswordField.setAttribute('type', type);

            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }
    
    // Password confirmation validation
    const confirmPassword = document.getElementById('confirm_password');
    confirmPassword.addEventListener('input', function() {
        if (this.value !== passwordField.value) {
            this.setCustomValidity('Passwords do not match');
        } else {
            this.setCustomValidity('');
        }
    });
    
    // Form validation
    const form = document.getElementById('registerForm');
    form.addEventListener('submit', function(e) {
        if (!validateForm('registerForm')) {
            e.preventDefault();
            showAlert('Please fill in all required fields.', 'danger');
            return false;
        }
        
        // Show loading state
        const submitBtn = form.querySelector('button[type="submit"]');
        showLoading(submitBtn);
    });
});
</script>

<?php include 'includes/footer.php'; ?>
