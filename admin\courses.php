<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add') {
            $course_code = sanitizeInput($_POST['course_code']);
            $course_name = sanitizeInput($_POST['course_name']);
            $description = sanitizeInput($_POST['description']);
            
            if (empty($course_code) || empty($course_name)) {
                $error_message = 'Please fill in all required fields.';
            } else {
                // Check if course code already exists
                $stmt = $conn->prepare("SELECT id FROM courses WHERE course_code = ?");
                $stmt->execute([$course_code]);
                if ($stmt->fetch()) {
                    $error_message = 'Course code already exists.';
                } else {
                    $stmt = $conn->prepare("INSERT INTO courses (course_code, course_name, description, status) VALUES (?, ?, ?, 'active')");
                    if ($stmt->execute([$course_code, $course_name, $description])) {
                        $success_message = 'Course added successfully!';
                        logActivity($_SESSION['user_id'], 'course_added', "Added course: $course_code - $course_name");
                    } else {
                        $error_message = 'Failed to add course.';
                    }
                }
            }
        } elseif ($action === 'edit') {
            $course_id = (int)$_POST['course_id'];
            $course_code = sanitizeInput($_POST['course_code']);
            $course_name = sanitizeInput($_POST['course_name']);
            $description = sanitizeInput($_POST['description']);
            $status = $_POST['status'];
            
            if (empty($course_code) || empty($course_name)) {
                $error_message = 'Please fill in all required fields.';
            } else {
                // Check if course code already exists for other courses
                $stmt = $conn->prepare("SELECT id FROM courses WHERE course_code = ? AND id != ?");
                $stmt->execute([$course_code, $course_id]);
                if ($stmt->fetch()) {
                    $error_message = 'Course code already exists.';
                } else {
                    $stmt = $conn->prepare("UPDATE courses SET course_code = ?, course_name = ?, description = ?, status = ? WHERE id = ?");
                    if ($stmt->execute([$course_code, $course_name, $description, $status, $course_id])) {
                        $success_message = 'Course updated successfully!';
                        logActivity($_SESSION['user_id'], 'course_updated', "Updated course: $course_code - $course_name");
                    } else {
                        $error_message = 'Failed to update course.';
                    }
                }
            }
        } elseif ($action === 'delete') {
            $course_id = (int)$_POST['course_id'];
            
            // Check if course is used by students or subjects
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE course_id = ?");
            $stmt->execute([$course_id]);
            $student_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM subjects WHERE course_id = ?");
            $stmt->execute([$course_id]);
            $subject_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($student_count > 0 || $subject_count > 0) {
                $error_message = "Cannot delete course. It is being used by $student_count student(s) and $subject_count subject(s).";
            } else {
                $stmt = $conn->prepare("DELETE FROM courses WHERE id = ?");
                if ($stmt->execute([$course_id])) {
                    $success_message = 'Course deleted successfully!';
                    logActivity($_SESSION['user_id'], 'course_deleted', "Deleted course ID: $course_id");
                } else {
                    $error_message = 'Failed to delete course.';
                }
            }
        }
    }
}

// Get all courses with statistics (ensure unique courses)
$stmt = $conn->prepare("SELECT c.*,
                       (SELECT COUNT(*) FROM users u WHERE u.course_id = c.id AND u.user_type = 'student') as student_count,
                       (SELECT COUNT(*) FROM subjects s WHERE s.course_id = c.id) as subject_count
                       FROM courses c
                       WHERE c.status = 'active'
                       GROUP BY c.course_code, c.course_name
                       ORDER BY c.course_code");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Course Management";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-graduation-cap text-2xl text-nsc-primary"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Course Management</h1>
                                <p class="text-gray-600">Manage academic courses and programs</p>
                            </div>
                        </div>
                        <button type="button" onclick="openAddModal()" class="bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Course
                        </button>
                    </div>
                </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

                <!-- Courses Table -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-graduation-cap text-nsc-primary"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Courses (<?php echo count($courses); ?> total)</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($courses)): ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-graduation-cap text-2xl text-gray-400"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No courses found</h3>
                                <p class="text-gray-500">Start by adding your first course.</p>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Course Code</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Course Name</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Description</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Students</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subjects</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Created</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($courses as $course): ?>
                                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                                <td class="py-4 px-4 font-semibold text-nsc-primary"><?php echo htmlspecialchars($course['course_code']); ?></td>
                                                <td class="py-4 px-4 text-gray-900 font-medium"><?php echo htmlspecialchars($course['course_name']); ?></td>
                                                <td class="py-4 px-4 text-gray-700">
                                                    <?php if ($course['description']): ?>
                                                        <?php echo htmlspecialchars(substr($course['description'], 0, 50)); ?>
                                                        <?php if (strlen($course['description']) > 50): ?>...<?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-gray-400">No description</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"><?php echo $course['student_count']; ?> students</span>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium"><?php echo $course['subject_count']; ?> subjects</span>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $course['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                        <?php echo ucfirst($course['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo date('M d, Y', strtotime($course['created_at'])); ?></td>
                                                <td class="py-4 px-4">
                                                    <div class="flex space-x-2">
                                                        <button type="button" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors"
                                                                onclick="editCourse(<?php echo htmlspecialchars(json_encode($course)); ?>)" title="Edit Course">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <a href="subjects.php?course=<?php echo $course['id']; ?>"
                                                           class="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm hover:bg-green-200 transition-colors" title="View Subjects">
                                                            <i class="fas fa-book"></i>
                                                        </a>
                                                        <a href="students.php?course=<?php echo $course['id']; ?>"
                                                           class="bg-purple-100 text-purple-700 px-3 py-1 rounded-lg text-sm hover:bg-purple-200 transition-colors" title="View Students">
                                                            <i class="fas fa-users"></i>
                                                        </a>
                                                        <?php if ($course['student_count'] == 0 && $course['subject_count'] == 0): ?>
                                                            <button type="button" class="bg-red-100 text-red-700 px-3 py-1 rounded-lg text-sm hover:bg-red-200 transition-colors"
                                                                    onclick="deleteCourse(<?php echo $course['id']; ?>, '<?php echo htmlspecialchars($course['course_code']); ?>')" title="Delete Course">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        <?php endif; ?>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<!-- Edit Course Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-lg w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Course Details</h3>
                    <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form method="POST" action="" id="editForm">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="course_id" id="editCourseId">

                    <div class="space-y-4">
                        <div>
                            <label for="editCourseCodeInput" class="block text-sm font-medium text-gray-700 mb-2">Course Code</label>
                            <input type="text" id="editCourseCodeInput" name="course_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                        </div>

                        <div>
                            <label for="editCourseNameInput" class="block text-sm font-medium text-gray-700 mb-2">Course Name</label>
                            <input type="text" id="editCourseNameInput" name="course_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                        </div>

                        <div>
                            <label for="editCourseDescInput" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="editCourseDescInput" name="description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"></textarea>
                        </div>

                        <div>
                            <label for="editCourseStatusInput" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select id="editCourseStatusInput" name="status" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-save mr-2"></i>Update Course
                        </button>
                        <button type="button" onclick="closeEditModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Delete Course</h3>
                    <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Are you sure?</h4>
                        <p class="text-gray-600">This action cannot be undone.</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-6">
                    You are about to delete course: <span id="deleteCourseCode" class="font-semibold"></span>
                </p>
                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                        <span class="text-yellow-800 text-sm">This will affect all students enrolled in this course.</span>
                    </div>
                </div>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="course_id" id="deleteCourseId">
                    <div class="flex space-x-3">
                        <button type="submit" class="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            Delete Course
                        </button>
                        <button type="button" onclick="closeDeleteModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Course Modal -->
<div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Add New Course</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form method="POST" action="" id="addForm">
                    <input type="hidden" name="action" value="add">

                    <div class="space-y-4">
                        <div>
                            <label for="addCourseCode" class="block text-sm font-medium text-gray-700 mb-2">Course Code *</label>
                            <input type="text" id="addCourseCode" name="course_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., BSIT, BSCS, BSBA">
                        </div>

                        <div>
                            <label for="addCourseName" class="block text-sm font-medium text-gray-700 mb-2">Course Name *</label>
                            <input type="text" id="addCourseName" name="course_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., Bachelor of Science in Information Technology">
                        </div>

                        <div>
                            <label for="addCourseDesc" class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea id="addCourseDesc" name="description" rows="3"
                                      class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                      placeholder="Brief description of the course..."></textarea>
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Course
                        </button>
                        <button type="button" onclick="closeAddModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editCourse(course) {
    const modal = document.getElementById('editModal');

    // Populate form fields
    document.getElementById('editCourseId').value = course.id;
    document.getElementById('editCourseCodeInput').value = course.course_code;
    document.getElementById('editCourseNameInput').value = course.course_name;
    document.getElementById('editCourseDescInput').value = course.description || '';
    document.getElementById('editCourseStatusInput').value = course.status;

    modal.classList.remove('hidden');
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
}

function deleteCourse(courseId, courseCode) {
    const modal = document.getElementById('deleteModal');

    document.getElementById('deleteCourseId').value = courseId;
    document.getElementById('deleteCourseCode').textContent = courseCode;

    modal.classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function openAddModal() {
    document.getElementById('addModal').classList.remove('hidden');
}

function closeAddModal() {
    document.getElementById('addModal').classList.add('hidden');
}

// Add loading states for edit and add forms
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit form submission
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
                submitBtn.disabled = true;
            }
        });
    }

    // Handle add form submission
    const addForm = document.getElementById('addForm');
    if (addForm) {
        addForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding...';
                submitBtn.disabled = true;
            }
        });
    }
});

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const addModal = document.getElementById('addModal');

    if (e.target === editModal) {
        closeEditModal();
    }
    if (e.target === deleteModal) {
        closeDeleteModal();
    }
    if (e.target === addModal) {
        closeAddModal();
    }
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
