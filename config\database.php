<?php
// Database configuration for Masbate Colleges Online Enrollment System
class Database {
    private $host = "localhost";
    private $db_name = "masbate_enrollment";
    private $username = "root";
    private $password = "";
    public $conn;

    public function getConnection() {
        $this->conn = null;
        try {
            $this->conn = new PDO("mysql:host=" . $this->host . ";dbname=" . $this->db_name, $this->username, $this->password);
            $this->conn->exec("set names utf8");
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        return $this->conn;
    }
}

// Create database and tables if they don't exist
function initializeDatabase() {
    try {
        // Connect without database first
        $pdo = new PDO("mysql:host=localhost", "root", "");
        $pdo->exec("CREATE DATABASE IF NOT EXISTS masbate_enrollment");
        $pdo->exec("USE masbate_enrollment");
        
        // Create users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id VARCHAR(20) UNIQUE,
            email VARCHAR(100) UNIQUE NOT NULL,
            password VARCHAR(255) NOT NULL,
            first_name VARCHAR(50) NOT NULL,
            last_name VARCHAR(50) NOT NULL,
            middle_name VARCHAR(50),
            contact_number VARCHAR(15),
            address TEXT,
            course_id INT,
            year_level INT,
            user_type ENUM('student', 'admin') DEFAULT 'student',
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )");

        // Create courses table
        $pdo->exec("CREATE TABLE IF NOT EXISTS courses (
            id INT AUTO_INCREMENT PRIMARY KEY,
            course_code VARCHAR(10) NOT NULL,
            course_name VARCHAR(100) NOT NULL,
            description TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_course (course_code, course_name)
        )");

        // Create subjects table
        $pdo->exec("CREATE TABLE IF NOT EXISTS subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            subject_code VARCHAR(20) NOT NULL,
            subject_name VARCHAR(100) NOT NULL,
            units INT NOT NULL,
            course_id INT,
            year_level INT,
            semester INT,
            prerequisite VARCHAR(100),
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (course_id) REFERENCES courses(id)
        )");

        // Create enrollments table
        $pdo->exec("CREATE TABLE IF NOT EXISTS enrollments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            semester INT NOT NULL,
            school_year VARCHAR(20) NOT NULL,
            total_units INT DEFAULT 0,
            total_fees DECIMAL(10,2) DEFAULT 0.00,
            payment_proof VARCHAR(255),
            status ENUM('pending', 'approved', 'returned') DEFAULT 'pending',
            remarks TEXT,
            submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            approved_at TIMESTAMP NULL,
            approved_by INT NULL,
            FOREIGN KEY (student_id) REFERENCES users(id),
            FOREIGN KEY (approved_by) REFERENCES users(id)
        )");

        // Create enrollment_subjects table
        $pdo->exec("CREATE TABLE IF NOT EXISTS enrollment_subjects (
            id INT AUTO_INCREMENT PRIMARY KEY,
            enrollment_id INT NOT NULL,
            subject_id INT NOT NULL,
            FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE,
            FOREIGN KEY (subject_id) REFERENCES subjects(id)
        )");

        // Insert default admin user
        $admin_password = password_hash('admin123', PASSWORD_DEFAULT);
        $pdo->exec("INSERT IGNORE INTO users (student_id, email, password, first_name, last_name, user_type) 
                   VALUES ('ADMIN001', '<EMAIL>', '$admin_password', 'System', 'Administrator', 'admin')");

        // Insert sample courses
        $pdo->exec("INSERT IGNORE INTO courses (course_code, course_name, description) VALUES
                   ('BSIT', 'Bachelor of Science in Information Technology', 'Four-year degree program in IT'),
                   ('BSCS', 'Bachelor of Science in Computer Science', 'Four-year degree program in Computer Science'),
                   ('BSA', 'Bachelor of Science in Accountancy', 'Four-year degree program in Accountancy'),
                   ('BSBA', 'Bachelor of Science in Business Administration', 'Four-year degree program in Business Administration')");

        // Insert sample subjects for BSIT
        $pdo->exec("INSERT IGNORE INTO subjects (subject_code, subject_name, units, course_id, year_level, semester, status) VALUES
                   ('IT101', 'Introduction to Computing', 3, 1, 1, 1, 'active'),
                   ('IT102', 'Computer Programming 1', 3, 1, 1, 1, 'active'),
                   ('MATH101', 'College Algebra', 3, 1, 1, 1, 'active'),
                   ('ENG101', 'English Communication', 3, 1, 1, 1, 'active'),
                   ('PE101', 'Physical Education 1', 2, 1, 1, 1, 'active'),
                   ('IT103', 'Computer Programming 2', 3, 1, 1, 2, 'active'),
                   ('IT104', 'Data Structures', 3, 1, 1, 2, 'active'),
                   ('MATH102', 'Statistics', 3, 1, 1, 2, 'active'),
                   ('ENG102', 'Technical Writing', 3, 1, 1, 2, 'active'),
                   ('PE102', 'Physical Education 2', 2, 1, 1, 2, 'active')");

        // Create activity_logs table
        $pdo->exec("CREATE TABLE IF NOT EXISTS activity_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            action VARCHAR(100) NOT NULL,
            details TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )");

        // Create notifications table
        $pdo->exec("CREATE TABLE IF NOT EXISTS notifications (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id INT NOT NULL,
            subject VARCHAR(255) NOT NULL,
            message TEXT NOT NULL,
            is_read BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users(id)
        )");

        // Create student_grades table
        $pdo->exec("CREATE TABLE IF NOT EXISTS student_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            subject_id INT NOT NULL,
            semester INT NOT NULL,
            school_year VARCHAR(20) NOT NULL,
            grade DECIMAL(5,2) NOT NULL,
            remarks ENUM('PASSED', 'FAILED', 'INCOMPLETE', 'DROPPED') DEFAULT 'PASSED',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES users(id),
            FOREIGN KEY (subject_id) REFERENCES subjects(id)
        )");

        // Create payments table
        $pdo->exec("CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            enrollment_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method ENUM('cash', 'check', 'bank_transfer', 'gcash', 'credit_card', 'online_banking') NOT NULL,
            reference_number VARCHAR(100),
            payment_date DATE NOT NULL,
            status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed',
            notes TEXT,
            recorded_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (enrollment_id) REFERENCES enrollments(id),
            FOREIGN KEY (recorded_by) REFERENCES users(id)
        )");

        // Add missing columns to enrollments table
        $pdo->exec("ALTER TABLE enrollments
                   ADD COLUMN IF NOT EXISTS verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending',
                   ADD COLUMN IF NOT EXISTS verification_notes TEXT,
                   ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP NULL,
                   ADD COLUMN IF NOT EXISTS verified_by INT NULL,
                   ADD COLUMN IF NOT EXISTS course_id INT NULL,
                   ADD FOREIGN KEY IF NOT EXISTS (verified_by) REFERENCES users(id),
                   ADD FOREIGN KEY IF NOT EXISTS (course_id) REFERENCES courses(id)");

        return true;
    } catch(PDOException $e) {
        echo "Database initialization error: " . $e->getMessage();
        return false;
    }
}

// Note: Database initialization is handled by install.php
// This prevents conflicts with the installation script
?>
