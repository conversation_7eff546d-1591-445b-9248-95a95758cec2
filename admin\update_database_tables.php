<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$message = '';
$message_type = '';

try {
    // Create student_grades table if not exists
    $conn->exec("CREATE TABLE IF NOT EXISTS student_grades (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        subject_id INT NOT NULL,
        semester INT NOT NULL,
        school_year VARCHAR(20) NOT NULL,
        grade DECIMAL(5,2) NOT NULL,
        remarks ENUM('PASSED', 'FAILED', 'INCOMPLETE', 'DROPPED') DEFAULT 'PASSED',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        FOREIG<PERSON> KEY (student_id) REFERENCES users(id),
        FOREIGN KEY (subject_id) REFERENCES subjects(id)
    )");

    // Create payments table if not exists
    $conn->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        enrollment_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('cash', 'check', 'bank_transfer', 'gcash', 'credit_card', 'online_banking') NOT NULL,
        reference_number VARCHAR(100),
        payment_date DATE NOT NULL,
        status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed',
        notes TEXT,
        recorded_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (enrollment_id) REFERENCES enrollments(id),
        FOREIGN KEY (recorded_by) REFERENCES users(id)
    )");

    // Check if columns exist before adding them
    $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE 'verification_status'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE enrollments ADD COLUMN verification_status ENUM('pending', 'verified', 'rejected') DEFAULT 'pending'");
    }

    $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE 'verification_notes'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE enrollments ADD COLUMN verification_notes TEXT");
    }

    $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE 'verified_at'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE enrollments ADD COLUMN verified_at TIMESTAMP NULL");
    }

    $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE 'verified_by'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE enrollments ADD COLUMN verified_by INT NULL");
    }

    $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE 'course_id'");
    if ($result->rowCount() == 0) {
        $conn->exec("ALTER TABLE enrollments ADD COLUMN course_id INT NULL");
    }

    // Add foreign key constraints if they don't exist
    try {
        $conn->exec("ALTER TABLE enrollments ADD CONSTRAINT fk_enrollments_verified_by FOREIGN KEY (verified_by) REFERENCES users(id)");
    } catch (Exception $e) {
        // Foreign key might already exist
    }

    try {
        $conn->exec("ALTER TABLE enrollments ADD CONSTRAINT fk_enrollments_course_id FOREIGN KEY (course_id) REFERENCES courses(id)");
    } catch (Exception $e) {
        // Foreign key might already exist
    }

    $message = "Database tables updated successfully!";
    $message_type = "success";

} catch (Exception $e) {
    $message = "Error updating database: " . $e->getMessage();
    $message_type = "error";
}

$page_title = "Database Update";
?>

<?php include '../includes/admin_layout_start.php'; ?>

<div class="flex-1 p-8">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="mb-8">
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl shadow-xl text-white p-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">Database Update</h1>
                        <p class="text-blue-100 text-lg">Update database tables and structure</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-database text-4xl text-white opacity-80"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <?php if ($message): ?>
            <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                <div class="flex items-center">
                    <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                    <?php echo htmlspecialchars($message); ?>
                </div>
            </div>
        <?php endif; ?>

        <!-- Database Status -->
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h2 class="text-xl font-bold text-gray-800 mb-4">Database Status</h2>
            
            <?php
            // Check table status
            $tables = ['users', 'courses', 'subjects', 'enrollments', 'enrollment_subjects', 'student_grades', 'payments', 'activity_logs', 'notifications'];
            ?>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <?php foreach ($tables as $table): ?>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <?php
                        try {
                            $result = $conn->query("SELECT COUNT(*) as count FROM $table");
                            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
                            $status = 'success';
                            $icon = 'fa-check-circle';
                            $color = 'text-green-600';
                        } catch (Exception $e) {
                            $count = 'Error';
                            $status = 'error';
                            $icon = 'fa-times-circle';
                            $color = 'text-red-600';
                        }
                        ?>
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-semibold text-gray-800"><?php echo ucfirst($table); ?></h3>
                                <p class="text-sm text-gray-500">Records: <?php echo $count; ?></p>
                            </div>
                            <i class="fas <?php echo $icon; ?> text-xl <?php echo $color; ?>"></i>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>

            <div class="mt-6 flex space-x-4">
                <a href="dashboard.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                    <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                </a>
                <button onclick="location.reload()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                    <i class="fas fa-sync-alt mr-2"></i>Refresh Status
                </button>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/admin_layout_end.php'; ?>
