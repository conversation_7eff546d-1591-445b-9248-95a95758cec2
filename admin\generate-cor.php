<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get enrollment ID
$enrollment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($enrollment_id <= 0) {
    header('Location: enrollments.php?error=invalid_enrollment_id');
    exit();
}

// Get enrollment details with student and course information
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.middle_name, u.student_id, u.email, u.contact_number, u.address, u.year_level,
                       c.course_code, c.course_name 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE e.id = ? AND e.status = 'approved'");
$stmt->execute([$enrollment_id]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: enrollments.php?error=enrollment_not_found_or_not_approved');
    exit();
}

// Get enrolled subjects
$stmt = $conn->prepare("SELECT s.* FROM subjects s 
                       JOIN enrollment_subjects es ON s.id = es.subject_id 
                       WHERE es.enrollment_id = ? 
                       ORDER BY s.subject_code");
$stmt->execute([$enrollment_id]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get school information (you can modify this based on your needs)
$school_info = [
    'name' => 'Masbate Colleges',
    'address' => 'Masbate City, Masbate',
    'phone' => '(*************',
    'email' => '<EMAIL>',
    'logo' => '../assets/images/masbate-logo.png'
];

$page_title = "Certificate of Registration";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    <link href="../assets/css/style.css" rel="stylesheet">
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @media print {
            .no-print { display: none !important; }
            body { margin: 0; }
            .print-container { box-shadow: none !important; }
        }
        
        .cor-header {
            border-bottom: 3px solid #1e40af;
            margin-bottom: 20px;
            padding-bottom: 15px;
        }
        
        .cor-table {
            border-collapse: collapse;
            width: 100%;
        }
        
        .cor-table th,
        .cor-table td {
            border: 1px solid #374151;
            padding: 8px;
            text-align: left;
        }
        
        .cor-table th {
            background-color: #f3f4f6;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto py-8 px-4">
        <!-- Action Buttons -->
        <div class="no-print mb-6 flex justify-between items-center">
            <a href="enrollments.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors font-medium">
                <i class="fas fa-arrow-left mr-2"></i>Back to Enrollments
            </a>
            <div class="space-x-3">
                <button onclick="window.print()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                    <i class="fas fa-print mr-2"></i>Print COR
                </button>
                <button onclick="downloadPDF()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                    <i class="fas fa-download mr-2"></i>Download PDF
                </button>
            </div>
        </div>

        <!-- Certificate of Registration -->
        <div class="bg-white shadow-2xl rounded-lg print-container" id="corDocument">
            <div class="p-8">
                <!-- Header -->
                <div class="cor-header text-center">
                    <div class="flex items-center justify-center mb-4">
                        <img src="<?php echo $school_info['logo']; ?>" alt="NSC Logo" class="w-16 h-16 mr-4" onerror="this.style.display='none'">
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900"><?php echo $school_info['name']; ?></h1>
                            <p class="text-gray-600"><?php echo $school_info['address']; ?></p>
                            <p class="text-gray-600"><?php echo $school_info['phone']; ?> | <?php echo $school_info['email']; ?></p>
                        </div>
                    </div>
                    <h2 class="text-xl font-bold text-blue-600 uppercase tracking-wide">Certificate of Registration</h2>
                </div>

                <!-- Student Information -->
                <div class="grid grid-cols-2 gap-8 mb-6">
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">Student Information</h3>
                        <div class="space-y-2">
                            <p><span class="font-semibold">Student ID:</span> <?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                            <p><span class="font-semibold">Name:</span> 
                                <?php echo htmlspecialchars($enrollment['first_name'] . ' ' . 
                                    ($enrollment['middle_name'] ? $enrollment['middle_name'] . ' ' : '') . 
                                    $enrollment['last_name']); ?>
                            </p>
                            <p><span class="font-semibold">Course:</span> <?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></p>
                            <p><span class="font-semibold">Year Level:</span> <?php echo $enrollment['year_level']; ?><?php echo getOrdinalSuffix($enrollment['year_level']); ?> Year</p>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">Enrollment Details</h3>
                        <div class="space-y-2">
                            <p><span class="font-semibold">Semester:</span> <?php echo getSemesterName($enrollment['semester']); ?></p>
                            <p><span class="font-semibold">School Year:</span> <?php echo $enrollment['school_year']; ?></p>
                            <p><span class="font-semibold">Date Enrolled:</span> <?php echo date('F d, Y', strtotime($enrollment['approved_at'])); ?></p>
                            <p><span class="font-semibold">Total Units:</span> <?php echo $enrollment['total_units']; ?></p>
                        </div>
                    </div>
                </div>

                <!-- Subjects Table -->
                <div class="mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">Enrolled Subjects</h3>
                    <table class="cor-table">
                        <thead>
                            <tr>
                                <th>Subject Code</th>
                                <th>Subject Name</th>
                                <th>Units</th>
                                <th>Prerequisites</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($subjects as $subject): ?>
                                <tr>
                                    <td class="font-semibold"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                    <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                    <td class="text-center"><?php echo $subject['units']; ?></td>
                                    <td><?php echo htmlspecialchars($subject['prerequisite'] ?: 'None'); ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                        <tfoot>
                            <tr class="bg-blue-50">
                                <td colspan="2" class="font-bold">Total Units</td>
                                <td class="text-center font-bold text-blue-600"><?php echo $enrollment['total_units']; ?></td>
                                <td></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>

                <!-- Fees Summary -->
                <div class="mb-6">
                    <h3 class="text-lg font-bold text-gray-900 mb-3 border-b border-gray-300 pb-1">Fees Summary</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold">Total Fees:</span>
                            <span class="text-xl font-bold text-blue-600"><?php echo formatCurrency($enrollment['total_fees']); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="mt-8 pt-6 border-t border-gray-300">
                    <div class="grid grid-cols-2 gap-8">
                        <div class="text-center">
                            <div class="border-b border-gray-400 mb-2 pb-1 w-48 mx-auto"></div>
                            <p class="text-sm font-semibold">Registrar</p>
                            <p class="text-xs text-gray-600">Date: <?php echo date('F d, Y'); ?></p>
                        </div>
                        <div class="text-center">
                            <div class="border-b border-gray-400 mb-2 pb-1 w-48 mx-auto"></div>
                            <p class="text-sm font-semibold">Student Signature</p>
                            <p class="text-xs text-gray-600">Date: _______________</p>
                        </div>
                    </div>
                    <div class="text-center mt-6">
                        <p class="text-xs text-gray-500">This is an official document. Any alteration or falsification is punishable by law.</p>
                        <p class="text-xs text-gray-500">Generated on: <?php echo date('F d, Y g:i A'); ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function downloadPDF() {
            // Simple implementation - you can enhance this with a proper PDF library
            alert('PDF download functionality can be implemented using libraries like jsPDF or server-side PDF generation.');
            // For now, suggest using print to PDF
            if (confirm('Would you like to print this document? You can save it as PDF from the print dialog.')) {
                window.print();
            }
        }
    </script>
</body>
</html>
