<?php
// Database Check Script for Masbate Colleges Online Enrollment System

try {
    $pdo = new PDO("mysql:host=localhost;dbname=masbate_enrollment", "root", "");
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "<h2>Database Connection: SUCCESS</h2>";
    
    // Check users table
    $stmt = $pdo->query("SELECT * FROM users");
    $users = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Users Table (" . count($users) . " records):</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Student ID</th><th>Email</th><th>Name</th><th>User Type</th><th>Course ID</th><th>Year Level</th></tr>";
    
    foreach ($users as $user) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($user['id']) . "</td>";
        echo "<td>" . htmlspecialchars($user['student_id']) . "</td>";
        echo "<td>" . htmlspecialchars($user['email']) . "</td>";
        echo "<td>" . htmlspecialchars($user['first_name'] . ' ' . $user['last_name']) . "</td>";
        echo "<td>" . htmlspecialchars($user['user_type']) . "</td>";
        echo "<td>" . htmlspecialchars($user['course_id']) . "</td>";
        echo "<td>" . htmlspecialchars($user['year_level']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check courses table
    $stmt = $pdo->query("SELECT * FROM courses");
    $courses = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo "<h3>Courses Table (" . count($courses) . " records):</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>ID</th><th>Course Code</th><th>Course Name</th><th>Status</th></tr>";
    
    foreach ($courses as $course) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($course['id']) . "</td>";
        echo "<td>" . htmlspecialchars($course['course_code']) . "</td>";
        echo "<td>" . htmlspecialchars($course['course_name']) . "</td>";
        echo "<td>" . htmlspecialchars($course['status']) . "</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // Check subjects table
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM subjects");
    $subject_count = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<h3>Subjects Table: " . $subject_count['count'] . " records</h3>";
    
} catch (Exception $e) {
    echo "<h2>Database Connection: FAILED</h2>";
    echo "<p>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}
?>

<style>
    body { font-family: Arial, sans-serif; margin: 20px; }
    table { margin: 10px 0; }
    th, td { padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>

<a href="install.php">Back to Installation</a> | 
<a href="reset_installation.php">Reset Installation</a> | 
<a href="index.php">Homepage</a>
