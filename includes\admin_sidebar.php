<?php
// Get current page name for active state
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Modern Admin Sidebar -->
<div class="w-64 bg-gradient-to-b from-nsc-primary to-nsc-dark min-h-screen shadow-xl">
    <div class="p-6">
        <!-- Admin Panel Header -->
        <div class="mb-8">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-user-shield text-white text-lg"></i>
                </div>
                <div>
                    <h3 class="text-white font-bold text-lg">Admin Panel</h3>
                    <p class="text-green-200 text-sm">Management System</p>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="space-y-2">
            <a href="dashboard.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'dashboard.php' ? 'text-white bg-white bg-opacity-20' : 'text-blue-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-tachometer-alt mr-3 <?php echo $current_page == 'dashboard.php' ? 'text-blue-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Dashboard</span>
            </a>

            <!-- Enrollment Management -->
            <div class="pt-4 pb-2">
                <p class="text-green-300 text-xs font-semibold uppercase tracking-wider px-4">Enrollment</p>
            </div>
            <a href="enrollments.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'enrollments.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-clipboard-list mr-3 <?php echo $current_page == 'enrollments.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Enrollments</span>
            </a>
            <a href="students.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'students.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-user-graduate mr-3 <?php echo $current_page == 'students.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Students</span>
            </a>

            <!-- Academic Management -->
            <div class="pt-4 pb-2">
                <p class="text-green-300 text-xs font-semibold uppercase tracking-wider px-4">Academic</p>
            </div>
            <a href="subjects.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'subjects.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-book mr-3 <?php echo $current_page == 'subjects.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Subjects</span>
            </a>
            <a href="courses.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'courses.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-graduation-cap mr-3 <?php echo $current_page == 'courses.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Courses</span>
            </a>

            <!-- Department Modules -->
            <div class="pt-4 pb-2">
                <p class="text-green-300 text-xs font-semibold uppercase tracking-wider px-4">Departments</p>
            </div>
            <a href="records.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'records.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-folder-open mr-3 <?php echo $current_page == 'records.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Records</span>
            </a>
            <a href="registrar.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'registrar.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-stamp mr-3 <?php echo $current_page == 'registrar.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Registrar</span>
            </a>
            <a href="accounting.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'accounting.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-calculator mr-3 <?php echo $current_page == 'accounting.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Accounting</span>
            </a>

            <!-- Reports & Settings -->
            <div class="pt-4 pb-2">
                <p class="text-green-300 text-xs font-semibold uppercase tracking-wider px-4">System</p>
            </div>
            <a href="reports.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'reports.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-chart-bar mr-3 <?php echo $current_page == 'reports.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Reports</span>
            </a>
        </nav>

        <!-- Logout Menu -->
        <div class="mt-auto pt-6 border-t border-white border-opacity-20">
            <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 hover:bg-red-500 hover:bg-opacity-20 hover:text-white rounded-lg transition-all duration-300">
                <i class="fas fa-sign-out-alt mr-3"></i>
                <span class="font-medium">Logout</span>
            </a>
        </div>
    </div>
</div>
