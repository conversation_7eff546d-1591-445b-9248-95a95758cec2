<?php
function generateFinancialReport($conn) {
    // Get financial data
    $current_month = date('Y-m');
    $current_year = date('Y');
    
    // Total revenue
    $revenue_stmt = $conn->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE status = 'confirmed'");
    $revenue_stmt->execute();
    $total_revenue = $revenue_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Monthly revenue
    $monthly_stmt = $conn->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE status = 'confirmed' AND DATE_FORMAT(payment_date, '%Y-%m') = ?");
    $monthly_stmt->execute([$current_month]);
    $monthly_revenue = $monthly_stmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // Outstanding balances
    $outstanding_stmt = $conn->prepare("SELECT COALESCE(SUM(e.total_fees - COALESCE(p.paid_amount, 0)), 0) as outstanding 
                                       FROM enrollments e 
                                       LEFT JOIN (SELECT enrollment_id, SUM(amount) as paid_amount FROM payments WHERE status = 'confirmed' GROUP BY enrollment_id) p ON e.id = p.enrollment_id 
                                       WHERE e.status = 'approved'");
    $outstanding_stmt->execute();
    $outstanding_balance = $outstanding_stmt->fetch(PDO::FETCH_ASSOC)['outstanding'];
    
    // Payment methods breakdown
    $methods_stmt = $conn->prepare("SELECT payment_method, COUNT(*) as count, SUM(amount) as total 
                                   FROM payments 
                                   WHERE status = 'confirmed' 
                                   GROUP BY payment_method 
                                   ORDER BY total DESC");
    $methods_stmt->execute();
    $payment_methods = $methods_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Monthly breakdown for current year
    $monthly_breakdown_stmt = $conn->prepare("SELECT DATE_FORMAT(payment_date, '%Y-%m') as month, SUM(amount) as total 
                                             FROM payments 
                                             WHERE status = 'confirmed' AND YEAR(payment_date) = ? 
                                             GROUP BY DATE_FORMAT(payment_date, '%Y-%m') 
                                             ORDER BY month");
    $monthly_breakdown_stmt->execute([$current_year]);
    $monthly_breakdown = $monthly_breakdown_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Generate HTML report
    $html = generateFinancialReportHTML($total_revenue, $monthly_revenue, $outstanding_balance, $payment_methods, $monthly_breakdown);
    
    // Set headers for PDF download
    header('Content-Type: text/html');
    header('Content-Disposition: inline; filename="financial_report_' . date('Y-m-d') . '.html"');
    
    echo $html;
}

function generateFinancialReportHTML($total_revenue, $monthly_revenue, $outstanding_balance, $payment_methods, $monthly_breakdown) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Financial Report - <?php echo date('F Y'); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .school-name { font-size: 24px; font-weight: bold; color: #16a34a; }
            .report-title { font-size: 18px; font-weight: bold; margin: 20px 0; }
            .summary-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin: 20px 0; }
            .summary-card { border: 1px solid #ddd; padding: 20px; border-radius: 8px; background: #f8f9fa; }
            .summary-card h3 { margin: 0 0 10px 0; color: #16a34a; }
            .summary-card .amount { font-size: 24px; font-weight: bold; color: #333; }
            .table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .table th, .table td { border: 1px solid #ddd; padding: 12px; text-align: left; }
            .table th { background-color: #f8f9fa; font-weight: bold; }
            .section { margin: 30px 0; }
            @media print { body { margin: 0; } }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="school-name">MASBATE COLLEGES</div>
            <div>Accounting Office</div>
            <div class="report-title">FINANCIAL REPORT</div>
            <div>Generated on: <?php echo date('F d, Y'); ?></div>
        </div>

        <div class="summary-grid">
            <div class="summary-card">
                <h3>Total Revenue</h3>
                <div class="amount"><?php echo formatCurrency($total_revenue); ?></div>
            </div>
            <div class="summary-card">
                <h3>This Month Revenue</h3>
                <div class="amount"><?php echo formatCurrency($monthly_revenue); ?></div>
            </div>
            <div class="summary-card">
                <h3>Outstanding Balance</h3>
                <div class="amount"><?php echo formatCurrency($outstanding_balance); ?></div>
            </div>
            <div class="summary-card">
                <h3>Collection Rate</h3>
                <div class="amount"><?php echo $total_revenue > 0 ? number_format(($total_revenue / ($total_revenue + $outstanding_balance)) * 100, 1) : 0; ?>%</div>
            </div>
        </div>

        <div class="section">
            <h3>Payment Methods Breakdown</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Payment Method</th>
                        <th>Number of Transactions</th>
                        <th>Total Amount</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($payment_methods as $method): 
                        $percentage = $total_revenue > 0 ? ($method['total'] / $total_revenue) * 100 : 0;
                    ?>
                    <tr>
                        <td><?php echo ucfirst(str_replace('_', ' ', $method['payment_method'])); ?></td>
                        <td><?php echo number_format($method['count']); ?></td>
                        <td><?php echo formatCurrency($method['total']); ?></td>
                        <td><?php echo number_format($percentage, 1); ?>%</td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div class="section">
            <h3>Monthly Revenue Breakdown (<?php echo date('Y'); ?>)</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>Month</th>
                        <th>Revenue</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($monthly_breakdown as $month): ?>
                    <tr>
                        <td><?php echo date('F Y', strtotime($month['month'] . '-01')); ?></td>
                        <td><?php echo formatCurrency($month['total']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <div style="margin-top: 50px; text-align: center;">
            <div style="border-top: 1px solid #000; width: 200px; margin: 0 auto; padding-top: 10px;">
                <strong>Accounting Officer</strong>
            </div>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
            This is an official financial report generated by the Masbate Colleges Student Information System.
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
