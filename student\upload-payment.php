<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get payment settings
$payment_settings = getPaymentSettings();

// Get enrollment ID from URL
$enrollment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Verify enrollment belongs to current user
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.student_id, c.course_code, c.course_name 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE e.id = ? AND e.student_id = ?");
$stmt->execute([$enrollment_id, $_SESSION['user_id']]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: dashboard.php');
    exit();
}

// Get selected subjects
$stmt = $conn->prepare("SELECT s.* FROM subjects s 
                       JOIN enrollment_subjects es ON s.id = es.subject_id 
                       WHERE es.enrollment_id = ? 
                       ORDER BY s.subject_code");
$stmt->execute([$enrollment_id]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!isset($_FILES['payment_proof']) || $_FILES['payment_proof']['error'] !== UPLOAD_ERR_OK) {
        $error_message = 'Please select a payment proof file.';
    } else {
        // Create uploads directory if it doesn't exist
        $upload_dir = '../uploads/payments';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $upload_result = uploadFile($_FILES['payment_proof'], $upload_dir, ['jpg', 'jpeg', 'png', 'pdf']);
        
        if ($upload_result['success']) {
            // Update enrollment with payment proof
            $stmt = $conn->prepare("UPDATE enrollments SET payment_proof = ? WHERE id = ?");
            if ($stmt->execute([$upload_result['filename'], $enrollment_id])) {
                // Log activity
                logActivity($_SESSION['user_id'], 'payment_uploaded', "Uploaded payment proof for enrollment ID: $enrollment_id");
                
                $success_message = 'Payment proof uploaded successfully! Your enrollment is now pending approval.';
                
                // Refresh enrollment data
                $stmt = $conn->prepare("SELECT * FROM enrollments WHERE id = ?");
                $stmt->execute([$enrollment_id]);
                $enrollment = array_merge($enrollment, $stmt->fetch(PDO::FETCH_ASSOC));
            } else {
                $error_message = 'Failed to save payment proof. Please try again.';
            }
        } else {
            $error_message = $upload_result['message'];
        }
    }
}

$page_title = "Upload Payment Proof";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .file-upload-area {
            border: 2px dashed #d1d5db;
            transition: all 0.3s ease;
        }
        .file-upload-area.dragover {
            border-color: #10b981;
            background-color: #f0fdf4;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-upload text-2xl text-blue-600"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Upload Payment Proof</h1>
                            <p class="text-gray-600">Submit your payment proof for enrollment verification</p>
                        </div>
                    </div>
                    <a href="dashboard.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                    </a>
                </div>
            </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>
            
            <!-- Enrollment Summary -->
            <div class="bg-white rounded-2xl shadow-lg mb-8">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-blue-600"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-900">Enrollment Summary</h2>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Student Name</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Student ID</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Course</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></p>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div>
                                <span class="text-sm font-medium text-gray-500">Semester</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo getSemesterName($enrollment['semester']); ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">School Year</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo $enrollment['school_year']; ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Total Units</span>
                                <p class="text-lg font-semibold text-gray-900"><?php echo $enrollment['total_units']; ?></p>
                            </div>
                            <div>
                                <span class="text-sm font-medium text-gray-500">Total Fees</span>
                                <p class="text-2xl font-bold text-green-600"><?php echo formatCurrency($enrollment['total_fees']); ?></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Selected Subjects -->
            <div class="bg-white rounded-2xl shadow-lg mb-8">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-book text-green-600"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-900">Selected Subjects</h2>
                    </div>
                </div>
                <div class="p-6">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b border-gray-200">
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                    <th class="text-left py-3 px-4 font-semibold text-gray-700 w-20">Units</th>
                                </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-100">
                                <?php foreach ($subjects as $subject): ?>
                                    <tr class="hover:bg-gray-50 transition-colors">
                                        <td class="py-4 px-4 font-semibold text-gray-900"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                        <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                        <td class="py-4 px-4">
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                <?php echo $subject['units']; ?>
                                            </span>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <?php if ($enrollment['payment_proof']): ?>
                <!-- Payment Already Uploaded -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-green-200 bg-green-50">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-check-circle text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-green-800">Payment Proof Uploaded</h2>
                        </div>
                    </div>
                    <div class="p-8 text-center">
                        <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="fas fa-file-check text-3xl text-green-600"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-3">Payment proof has been uploaded successfully!</h3>
                        <p class="text-gray-600 mb-4">Your enrollment is now pending approval by the registrar.</p>
                        <div class="bg-gray-50 rounded-lg p-4 mb-6">
                            <p class="text-sm text-gray-600 mb-1">Uploaded File:</p>
                            <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['payment_proof']); ?></p>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-3 justify-center">
                            <a href="view-enrollment.php?id=<?php echo $enrollment['id']; ?>" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                <i class="fas fa-eye mr-2"></i>View Enrollment Details
                            </a>
                            <a href="dashboard.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <i class="fas fa-home mr-2"></i>Back to Dashboard
                            </a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <!-- Upload Payment Proof -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-upload text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Upload Payment Proof</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="max-w-md mx-auto">
                            <!-- Upload Payment Proof -->
                            <div class="border-2 border-green-200 rounded-xl p-8 hover:border-green-300 transition-colors">
                                <div class="text-center">
                                    <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                        <i class="fas fa-upload text-3xl text-green-600"></i>
                                    </div>
                                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Upload Payment Proof</h3>
                                    <p class="text-gray-600 mb-6">Pay via GCash, Bank Transfer, or Over-the-Counter and upload your receipt</p>
                                    <div class="space-y-3 mb-8">
                                        <div class="flex items-center justify-center text-gray-600">
                                            <i class="fas fa-mobile-alt mr-3 text-blue-500"></i>
                                            <span>GCash, Maya, GrabPay</span>
                                        </div>
                                        <div class="flex items-center justify-center text-gray-600">
                                            <i class="fas fa-university mr-3 text-blue-500"></i>
                                            <span>Bank Transfer</span>
                                        </div>
                                        <div class="flex items-center justify-center text-gray-600">
                                            <i class="fas fa-store mr-3 text-blue-500"></i>
                                            <span>Over-the-Counter</span>
                                        </div>
                                    </div>
                                    <button onclick="showUploadForm()"
                                            class="w-full bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg">
                                        <i class="fas fa-upload mr-2"></i>Upload Payment Proof
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Payment Upload Form (Initially Hidden) -->
                <div id="uploadForm" class="hidden grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-lg">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-upload text-blue-600"></i>
                                    </div>
                                    <h2 class="text-xl font-bold text-gray-900">Upload Payment Proof</h2>
                                </div>
                            </div>
                            <div class="p-6">
                                <form method="POST" action="" enctype="multipart/form-data" id="paymentForm">
                                    <div class="mb-6">
                                        <label for="payment_proof" class="block text-sm font-medium text-gray-700 mb-3">
                                            <i class="fas fa-file-upload mr-1"></i>Payment Proof <span class="text-red-500">*</span>
                                        </label>
                                        <div class="file-upload-area bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center cursor-pointer hover:bg-gray-100 transition-colors" onclick="document.getElementById('payment_proof').click()">
                                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                                            <p class="text-lg font-medium text-gray-700 mb-2">Click to select file or drag and drop</p>
                                            <p class="text-sm text-gray-500">Accepted formats: JPG, PNG, PDF (Max 5MB)</p>
                                        </div>
                                        <input type="file" class="hidden" id="payment_proof" name="payment_proof"
                                               accept=".jpg,.jpeg,.png,.pdf" required data-preview="file-preview">
                                    </div>

                                    <div id="file-preview" class="mb-6"></div>

                                    <button type="submit" class="w-full bg-green-600 text-white px-6 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg btn-hover">
                                        <i class="fas fa-upload mr-2"></i>Upload Payment Proof
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                    
                    <div class="space-y-6">
                        <!-- Payment Instructions -->
                        <div class="bg-white rounded-2xl shadow-lg">
                            <div class="p-6 border-b border-blue-200 bg-blue-50">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-info-circle text-blue-600"></i>
                                    </div>
                                    <h3 class="text-lg font-bold text-blue-800">Payment Instructions</h3>
                                </div>
                            </div>
                            <div class="p-6">
                                <h4 class="font-semibold text-gray-900 mb-4">Accepted Payment Methods:</h4>
                                <div class="space-y-3 mb-6">
                                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-mobile-alt text-blue-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900">GCash</p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($payment_settings['gcash_number']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($payment_settings['gcash_name']); ?></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-university text-green-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900">Bank Transfer</p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($payment_settings['bank_name']); ?> Account #<?php echo htmlspecialchars($payment_settings['bank_account_number']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($payment_settings['bank_account_name']); ?></p>
                                        </div>
                                    </div>
                                    <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                        <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                            <i class="fas fa-money-bill text-yellow-600"></i>
                                        </div>
                                        <div>
                                            <p class="font-semibold text-gray-900">Over-the-Counter</p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($payment_settings['otc_location']); ?></p>
                                            <p class="text-xs text-gray-500"><?php echo htmlspecialchars($payment_settings['otc_hours']); ?></p>
                                        </div>
                                    </div>
                                </div>

                                <h4 class="font-semibold text-gray-900 mb-3">Upload Requirements:</h4>
                                <ul class="text-sm text-gray-600 space-y-1 mb-4">
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Clear photo/scan of receipt</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Amount must match total fees</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>File size max 5MB</li>
                                    <li class="flex items-center"><i class="fas fa-check text-green-500 mr-2"></i>Formats: JPG, PNG, PDF</li>
                                </ul>

                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-1"></i>
                                        <div class="text-sm text-yellow-800">
                                            <p class="mb-2"><strong>Important:</strong> Ensure payment amount matches the total fees exactly: <span class="font-bold"><?php echo formatCurrency($enrollment['total_fees']); ?></span></p>
                                            <?php if (!empty($payment_settings['payment_instructions'])): ?>
                                                <p><?php echo htmlspecialchars($payment_settings['payment_instructions']); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Show upload form function
function showUploadForm() {
    document.getElementById('uploadForm').classList.remove('hidden');
    document.getElementById('uploadForm').scrollIntoView({ behavior: 'smooth' });
}
document.addEventListener('DOMContentLoaded', function() {
    const fileInput = document.getElementById('payment_proof');
    const fileUploadArea = document.querySelector('.file-upload-area');
    const filePreview = document.getElementById('file-preview');
    const form = document.getElementById('paymentForm');

    // File input change handler
    fileInput.addEventListener('change', function() {
        handleFileSelect(this.files[0]);
    });

    // File validation and preview
    function handleFileSelect(file) {
        if (!file) return;

        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
        if (!allowedTypes.includes(file.type)) {
            showAlert('Please select a valid file type (JPG, PNG, or PDF)', 'error');
            fileInput.value = '';
            return;
        }

        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showAlert('File size must be less than 5MB', 'error');
            fileInput.value = '';
            return;
        }

        updateUploadArea(file);
        showFilePreview(file);
    }

    // Update upload area appearance
    function updateUploadArea(file) {
        if (file && fileUploadArea) {
            fileUploadArea.innerHTML = `
                <i class="fas fa-file-check text-4xl text-green-600 mb-4"></i>
                <p class="text-lg font-medium text-green-700 mb-2">File selected: ${file.name}</p>
                <p class="text-sm text-green-600">${formatFileSize(file.size)}</p>
            `;
            fileUploadArea.classList.remove('border-gray-300', 'bg-gray-50');
            fileUploadArea.classList.add('border-green-300', 'bg-green-50');
        }
    }

    // Show file preview
    function showFilePreview(file) {
        if (!filePreview) return;

        const fileSize = formatFileSize(file.size);
        const fileName = file.name;
        const fileType = file.type;

        let fileIcon = 'fas fa-file';
        if (fileType.includes('image')) {
            fileIcon = 'fas fa-file-image';
        } else if (fileType.includes('pdf')) {
            fileIcon = 'fas fa-file-pdf';
        }

        filePreview.innerHTML = `
            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="${fileIcon} text-green-600 text-xl"></i>
                    </div>
                    <div class="flex-1">
                        <h4 class="font-semibold text-green-800 mb-1">File Selected: ${fileName}</h4>
                        <p class="text-sm text-green-600">Size: ${fileSize}</p>
                    </div>
                    <button type="button" onclick="clearFileSelection()" class="text-red-500 hover:text-red-700 ml-4">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Show alert
    function showAlert(message, type) {
        const alertClass = type === 'error' ? 'bg-red-50 border-red-200 text-red-800' : 'bg-green-50 border-green-200 text-green-800';
        const iconClass = type === 'error' ? 'fas fa-exclamation-circle text-red-600' : 'fas fa-check-circle text-green-600';

        const alertDiv = document.createElement('div');
        alertDiv.className = `${alertClass} border px-4 py-3 rounded-lg mb-4`;
        alertDiv.innerHTML = `
            <div class="flex items-center">
                <i class="${iconClass} mr-2"></i>
                <span>${message}</span>
            </div>
        `;

        if (form) {
            form.insertBefore(alertDiv, form.firstChild);
            setTimeout(() => alertDiv.remove(), 5000);
        }
    }

    // Form submission
    if (form) {
        form.addEventListener('submit', function(e) {
            const file = fileInput.files[0];
            if (!file) {
                e.preventDefault();
                showAlert('Please select a payment proof file.', 'error');
                return false;
            }

            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Uploading...';
                submitBtn.classList.add('opacity-75', 'cursor-not-allowed');
            }
        });
    }

    // Drag and drop functionality
    if (fileUploadArea) {
        fileUploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            this.classList.add('border-blue-400', 'bg-blue-50');
            this.classList.remove('border-gray-300', 'bg-gray-50');
        });

        fileUploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            this.classList.remove('border-blue-400', 'bg-blue-50');
            this.classList.add('border-gray-300', 'bg-gray-50');
        });

        fileUploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            this.classList.remove('border-blue-400', 'bg-blue-50');
            this.classList.add('border-gray-300', 'bg-gray-50');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                fileInput.files = files;
                handleFileSelect(files[0]);
            }
        });
    }
});

// Clear file selection function
function clearFileSelection() {
    const fileInput = document.getElementById('payment_proof');
    const filePreview = document.getElementById('file-preview');
    const fileUploadArea = document.querySelector('.file-upload-area');

    if (fileInput) fileInput.value = '';
    if (filePreview) filePreview.innerHTML = '';

    if (fileUploadArea) {
        fileUploadArea.innerHTML = `
            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
            <p class="text-lg font-medium text-gray-700 mb-2">Click to select file or drag and drop</p>
            <p class="text-sm text-gray-500">Accepted formats: JPG, PNG, PDF (Max 5MB)</p>
        `;
        fileUploadArea.classList.remove('border-green-300', 'bg-green-50');
        fileUploadArea.classList.add('border-gray-300', 'bg-gray-50');
    }
}
</script>

</body>
</html>
