<?php
function generateTranscriptPDF($student, $grades) {
    // Simple HTML to PDF transcript generator
    $html = generateTranscriptHTML($student, $grades);
    
    // Set headers for PDF download
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="transcript_' . $student['student_id'] . '.pdf"');
    
    // For now, we'll output HTML that can be printed as PDF
    // In production, you would use a library like TCPDF or mPDF
    echo $html;
}

function generateTranscriptHTML($student, $grades) {
    $total_units = 0;
    $total_grade_points = 0;
    $passed_units = 0;
    
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Official Transcript - <?php echo htmlspecialchars($student['student_id']); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; }
            .school-name { font-size: 24px; font-weight: bold; color: #16a34a; }
            .document-title { font-size: 18px; font-weight: bold; margin: 20px 0; }
            .student-info { margin: 20px 0; }
            .grades-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .grades-table th, .grades-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            .grades-table th { background-color: #f8f9fa; font-weight: bold; }
            .summary { margin-top: 30px; }
            .signature-section { margin-top: 50px; }
            @media print { body { margin: 0; } }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="school-name">MASBATE COLLEGES</div>
            <div>Masbate City, Masbate, Philippines</div>
            <div>Tel: (************* | Email: <EMAIL></div>
            <div class="document-title">OFFICIAL TRANSCRIPT OF RECORDS</div>
        </div>

        <div class="student-info">
            <table style="width: 100%;">
                <tr>
                    <td><strong>Student Name:</strong> <?php echo htmlspecialchars($student['last_name'] . ', ' . $student['first_name'] . ' ' . $student['middle_name']); ?></td>
                    <td><strong>Student ID:</strong> <?php echo htmlspecialchars($student['student_id']); ?></td>
                </tr>
                <tr>
                    <td><strong>Address:</strong> <?php echo htmlspecialchars($student['address']); ?></td>
                    <td><strong>Date Generated:</strong> <?php echo date('F d, Y'); ?></td>
                </tr>
            </table>
        </div>

        <table class="grades-table">
            <thead>
                <tr>
                    <th>Subject Code</th>
                    <th>Subject Title</th>
                    <th>Units</th>
                    <th>Grade</th>
                    <th>Remarks</th>
                    <th>School Year</th>
                    <th>Semester</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($grades as $grade): 
                    $total_units += $grade['units'];
                    if ($grade['grade'] >= 75) {
                        $passed_units += $grade['units'];
                        $total_grade_points += ($grade['grade'] * $grade['units']);
                    }
                ?>
                <tr>
                    <td><?php echo htmlspecialchars($grade['subject_code']); ?></td>
                    <td><?php echo htmlspecialchars($grade['subject_name']); ?></td>
                    <td><?php echo $grade['units']; ?></td>
                    <td><?php echo $grade['grade']; ?></td>
                    <td><?php echo htmlspecialchars($grade['remarks']); ?></td>
                    <td><?php echo htmlspecialchars($grade['school_year']); ?></td>
                    <td><?php echo getSemesterName($grade['semester']); ?></td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>

        <div class="summary">
            <table style="width: 50%;">
                <tr>
                    <td><strong>Total Units Enrolled:</strong></td>
                    <td><?php echo $total_units; ?></td>
                </tr>
                <tr>
                    <td><strong>Total Units Passed:</strong></td>
                    <td><?php echo $passed_units; ?></td>
                </tr>
                <tr>
                    <td><strong>General Weighted Average:</strong></td>
                    <td><?php echo $passed_units > 0 ? number_format($total_grade_points / $passed_units, 2) : 'N/A'; ?></td>
                </tr>
            </table>
        </div>

        <div class="signature-section">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Registrar</strong>
                        </div>
                    </td>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Date Issued</strong><br>
                            <?php echo date('F d, Y'); ?>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
            This is an official document generated by the Masbate Colleges Student Information System.
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
