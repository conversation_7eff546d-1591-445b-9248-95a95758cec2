<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Get current admin user data
$stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'admin'");
$stmt->execute([$_SESSION['user_id']]);
$admin = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$admin) {
    header('Location: ../logout.php');
    exit();
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        // Debug: Log the action being performed
        error_log("Profile action: " . $action);
        
        if ($action === 'update_profile') {
            $first_name = sanitizeInput($_POST['first_name']);
            $last_name = sanitizeInput($_POST['last_name']);
            $middle_name = sanitizeInput($_POST['middle_name']);
            $email = sanitizeInput($_POST['email']);
            $contact_number = sanitizeInput($_POST['contact_number']);
            $address = sanitizeInput($_POST['address']);
            
            if (empty($first_name) || empty($last_name) || empty($email)) {
                $error_message = 'Please fill in all required fields.';
            } else {
                // Check if email already exists for other users
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $_SESSION['user_id']]);
                if ($stmt->fetch()) {
                    $error_message = 'Email address already exists.';
                } else {
                    try {
                        $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, middle_name = ?, email = ?, contact_number = ?, address = ? WHERE id = ?");
                        if ($stmt->execute([$first_name, $last_name, $middle_name, $email, $contact_number, $address, $_SESSION['user_id']])) {
                            $success_message = 'Profile updated successfully!';

                            // Update session variables
                            $_SESSION['first_name'] = $first_name;
                            $_SESSION['last_name'] = $last_name;
                            $_SESSION['email'] = $email;

                            // Refresh admin data
                            $stmt = $conn->prepare("SELECT * FROM users WHERE id = ? AND user_type = 'admin'");
                            $stmt->execute([$_SESSION['user_id']]);
                            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

                            logActivity($_SESSION['user_id'], 'profile_updated', 'Admin profile updated');
                        } else {
                            $error_message = 'Failed to update profile. Database error.';
                            error_log("Profile update failed for user ID: " . $_SESSION['user_id']);
                        }
                    } catch (Exception $e) {
                        $error_message = 'Failed to update profile. System error.';
                        error_log("Profile update exception: " . $e->getMessage());
                    }
                }
            }
        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $error_message = 'Please fill in all password fields.';
            } elseif ($new_password !== $confirm_password) {
                $error_message = 'New passwords do not match.';
            } elseif (strlen($new_password) < 6) {
                $error_message = 'New password must be at least 6 characters long.';
            } elseif (!password_verify($current_password, $admin['password'])) {
                $error_message = 'Current password is incorrect.';
            } else {
                try {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    if ($stmt->execute([$hashed_password, $_SESSION['user_id']])) {
                        $success_message = 'Password changed successfully!';
                        logActivity($_SESSION['user_id'], 'password_changed', 'Admin password changed');
                    } else {
                        $error_message = 'Failed to change password. Database error.';
                        error_log("Password change failed for user ID: " . $_SESSION['user_id']);
                    }
                } catch (Exception $e) {
                    $error_message = 'Failed to change password. System error.';
                    error_log("Password change exception: " . $e->getMessage());
                }
            }
        }
    }
}

$page_title = "Admin Profile";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-user-cog text-2xl text-nsc-primary"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Admin Profile</h1>
                            <p class="text-gray-600">Manage your account information and settings</p>
                        </div>
                    </div>
                </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <!-- Profile Information -->
                    <div class="lg:col-span-2">
                        <div class="bg-white rounded-2xl shadow-lg">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-user text-blue-600"></i>
                                    </div>
                                    <h2 class="text-xl font-bold text-gray-800">Profile Information</h2>
                                </div>
                            </div>
                            <div class="p-6">
                                <form method="POST" action="" id="profileForm">
                                    <input type="hidden" name="action" value="update_profile">
                                    
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                            <input type="text" id="first_name" name="first_name" required
                                                   value="<?php echo htmlspecialchars($admin['first_name']); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                            <input type="text" id="last_name" name="last_name" required
                                                   value="<?php echo htmlspecialchars($admin['last_name']); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="middle_name" class="block text-sm font-medium text-gray-700 mb-2">Middle Name</label>
                                            <input type="text" id="middle_name" name="middle_name"
                                                   value="<?php echo htmlspecialchars($admin['middle_name'] ?? ''); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                            <input type="email" id="email" name="email" required
                                                   value="<?php echo htmlspecialchars($admin['email']); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="contact_number" class="block text-sm font-medium text-gray-700 mb-2">Contact Number</label>
                                            <input type="tel" id="contact_number" name="contact_number"
                                                   value="<?php echo htmlspecialchars($admin['contact_number'] ?? ''); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="student_id" class="block text-sm font-medium text-gray-700 mb-2">Admin ID</label>
                                            <input type="text" id="student_id" name="student_id" readonly
                                                   value="<?php echo htmlspecialchars($admin['student_id']); ?>"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed">
                                        </div>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Address</label>
                                        <textarea id="address" name="address" rows="3"
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors"><?php echo htmlspecialchars($admin['address'] ?? ''); ?></textarea>
                                    </div>
                                    
                                    <div class="flex justify-end mt-6">
                                        <button type="submit" class="bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                                            <i class="fas fa-save mr-2"></i>Update Profile
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Account Summary & Password Change -->
                    <div class="space-y-8">
                        <!-- Account Summary -->
                        <div class="bg-white rounded-2xl shadow-lg">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-info-circle text-green-600"></i>
                                    </div>
                                    <h2 class="text-xl font-bold text-gray-800">Account Summary</h2>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Account Type:</span>
                                        <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium">Administrator</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Status:</span>
                                        <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">Active</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Member Since:</span>
                                        <span class="text-gray-900 font-medium"><?php echo date('M d, Y', strtotime($admin['created_at'])); ?></span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Change Password -->
                        <div class="bg-white rounded-2xl shadow-lg">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-lock text-yellow-600"></i>
                                    </div>
                                    <h2 class="text-xl font-bold text-gray-800">Change Password</h2>
                                </div>
                            </div>
                            <div class="p-6">
                                <form method="POST" action="" id="passwordForm">
                                    <input type="hidden" name="action" value="change_password">
                                    
                                    <div class="space-y-4">
                                        <div>
                                            <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">Current Password *</label>
                                            <input type="password" id="current_password" name="current_password" required
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">New Password *</label>
                                            <input type="password" id="new_password" name="new_password" required minlength="6"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                        
                                        <div>
                                            <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">Confirm New Password *</label>
                                            <input type="password" id="confirm_password" name="confirm_password" required minlength="6"
                                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                        </div>
                                    </div>
                                    
                                    <div class="mt-6">
                                        <button type="submit" class="w-full bg-yellow-600 text-white px-4 py-3 rounded-lg hover:bg-yellow-700 transition-colors font-medium">
                                            <i class="fas fa-key mr-2"></i>Change Password
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<script>
// Password confirmation validation
document.getElementById('passwordForm').addEventListener('submit', function(e) {
    const newPassword = document.getElementById('new_password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    
    if (newPassword !== confirmPassword) {
        e.preventDefault();
        alert('New passwords do not match!');
        return false;
    }
});

// Add loading states for forms
document.addEventListener('DOMContentLoaded', function() {
    const forms = document.querySelectorAll('#profileForm, #passwordForm');
    forms.forEach(form => {
        form.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
                submitBtn.disabled = true;
            }
        });
    });
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
