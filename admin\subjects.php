<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'add') {
            $subject_code = sanitizeInput($_POST['subject_code']);
            $subject_name = sanitizeInput($_POST['subject_name']);
            $units = (int)$_POST['units'];
            $course_id = (int)$_POST['course_id'];
            $year_level = (int)$_POST['year_level'];
            $semester = (int)$_POST['semester'];
            $prerequisite = sanitizeInput($_POST['prerequisite']);
            
            if (empty($subject_code) || empty($subject_name) || $units <= 0 || $course_id <= 0 || $year_level <= 0 || $semester <= 0) {
                $error_message = 'Please fill in all required fields.';
            } else {
                // Check if subject code already exists
                $stmt = $conn->prepare("SELECT id FROM subjects WHERE subject_code = ?");
                $stmt->execute([$subject_code]);
                if ($stmt->fetch()) {
                    $error_message = 'Subject code already exists.';
                } else {
                    $stmt = $conn->prepare("INSERT INTO subjects (subject_code, subject_name, units, course_id, year_level, semester, prerequisite, status) VALUES (?, ?, ?, ?, ?, ?, ?, 'active')");
                    if ($stmt->execute([$subject_code, $subject_name, $units, $course_id, $year_level, $semester, $prerequisite])) {
                        $success_message = 'Subject added successfully!';
                        logActivity($_SESSION['user_id'], 'subject_added', "Added subject: $subject_code - $subject_name");
                    } else {
                        $error_message = 'Failed to add subject.';
                    }
                }
            }
        } elseif ($action === 'edit') {
            $subject_id = (int)$_POST['subject_id'];
            $subject_code = sanitizeInput($_POST['subject_code']);
            $subject_name = sanitizeInput($_POST['subject_name']);
            $units = (int)$_POST['units'];
            $course_id = (int)$_POST['course_id'];
            $year_level = (int)$_POST['year_level'];
            $semester = (int)$_POST['semester'];
            $prerequisite = sanitizeInput($_POST['prerequisite']);
            $status = $_POST['status'];
            
            if (empty($subject_code) || empty($subject_name) || $units <= 0 || $course_id <= 0 || $year_level <= 0 || $semester <= 0) {
                $error_message = 'Please fill in all required fields.';
            } else {
                $stmt = $conn->prepare("UPDATE subjects SET subject_code = ?, subject_name = ?, units = ?, course_id = ?, year_level = ?, semester = ?, prerequisite = ?, status = ? WHERE id = ?");
                if ($stmt->execute([$subject_code, $subject_name, $units, $course_id, $year_level, $semester, $prerequisite, $status, $subject_id])) {
                    $success_message = 'Subject updated successfully!';
                    logActivity($_SESSION['user_id'], 'subject_updated', "Updated subject: $subject_code - $subject_name");
                } else {
                    $error_message = 'Failed to update subject.';
                }
            }
        } elseif ($action === 'delete') {
            $subject_id = (int)$_POST['subject_id'];
            
            // Check if subject is used in any enrollments
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollment_subjects WHERE subject_id = ?");
            $stmt->execute([$subject_id]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($result['count'] > 0) {
                $error_message = 'Cannot delete subject. It is being used in enrollments.';
            } else {
                $stmt = $conn->prepare("DELETE FROM subjects WHERE id = ?");
                if ($stmt->execute([$subject_id])) {
                    $success_message = 'Subject deleted successfully!';
                    logActivity($_SESSION['user_id'], 'subject_deleted', "Deleted subject ID: $subject_id");
                } else {
                    $error_message = 'Failed to delete subject.';
                }
            }
        }
    }
}

// Get filter parameters
$course_filter = isset($_GET['course']) ? (int)$_GET['course'] : 0;
$year_filter = isset($_GET['year']) ? (int)$_GET['year'] : 0;
$semester_filter = isset($_GET['semester']) ? (int)$_GET['semester'] : 0;
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';

// Build query with filters
$where_conditions = [];
$params = [];

if ($course_filter > 0) {
    $where_conditions[] = "s.course_id = ?";
    $params[] = $course_filter;
}
if ($year_filter > 0) {
    $where_conditions[] = "s.year_level = ?";
    $params[] = $year_filter;
}
if ($semester_filter > 0) {
    $where_conditions[] = "s.semester = ?";
    $params[] = $semester_filter;
}
if (!empty($status_filter)) {
    $where_conditions[] = "s.status = ?";
    $params[] = $status_filter;
}

$where_clause = !empty($where_conditions) ? "WHERE " . implode(" AND ", $where_conditions) : "";

// Get subjects with course information
$stmt = $conn->prepare("SELECT s.*, c.course_code, c.course_name 
                       FROM subjects s 
                       LEFT JOIN courses c ON s.course_id = c.id 
                       $where_clause
                       ORDER BY c.course_code, s.year_level, s.semester, s.subject_code");
$stmt->execute($params);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get all courses for dropdowns (ensure unique courses)
$stmt = $conn->prepare("SELECT * FROM courses WHERE status = 'active' GROUP BY course_code, course_name ORDER BY course_code");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Subject Management";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-book text-2xl text-nsc-primary"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Subject Management</h1>
                                <p class="text-gray-600">Manage course subjects and curriculum</p>
                            </div>
                        </div>
                        <button type="button" onclick="openAddModal()" class="bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Subject
                        </button>
                    </div>
                </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

                <!-- Filters -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Filter Subjects</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <form method="GET" action="" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Course</label>
                                    <select name="course" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Courses</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['id']; ?>" <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($course['course_code']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Year Level</label>
                                    <select name="year" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Years</option>
                                        <option value="1" <?php echo $year_filter == 1 ? 'selected' : ''; ?>>1st Year</option>
                                        <option value="2" <?php echo $year_filter == 2 ? 'selected' : ''; ?>>2nd Year</option>
                                        <option value="3" <?php echo $year_filter == 3 ? 'selected' : ''; ?>>3rd Year</option>
                                        <option value="4" <?php echo $year_filter == 4 ? 'selected' : ''; ?>>4th Year</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                                    <select name="semester" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Semesters</option>
                                        <option value="1" <?php echo $semester_filter == 1 ? 'selected' : ''; ?>>1st Semester</option>
                                        <option value="2" <?php echo $semester_filter == 2 ? 'selected' : ''; ?>>2nd Semester</option>
                                        <option value="3" <?php echo $semester_filter == 3 ? 'selected' : ''; ?>>Summer</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                    <select name="status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Status</option>
                                        <option value="active" <?php echo $status_filter == 'active' ? 'selected' : ''; ?>>Active</option>
                                        <option value="inactive" <?php echo $status_filter == 'inactive' ? 'selected' : ''; ?>>Inactive</option>
                                    </select>
                                </div>
                                <div class="flex items-end">
                                    <div class="w-full space-y-2">
                                        <button type="submit" class="w-full bg-nsc-primary text-white px-4 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                                            <i class="fas fa-search mr-2"></i>Filter
                                        </button>
                                        <a href="subjects.php" class="block w-full text-center border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                            <i class="fas fa-times mr-2"></i>Clear
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Subjects Table -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-table text-nsc-primary"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Subjects (<?php echo count($subjects); ?> found)</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($subjects)): ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-book text-2xl text-gray-400"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No subjects found</h3>
                                <p class="text-gray-500">No subjects match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Course</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Year</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Semester</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Prerequisites</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($subjects as $subject): ?>
                                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                                <td class="py-4 px-4 font-semibold text-gray-900"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo $subject['units']; ?></td>
                                                <td class="py-4 px-4">
                                                    <span class="px-3 py-1 bg-nsc-primary text-white rounded-full text-sm font-medium"><?php echo htmlspecialchars($subject['course_code']); ?></span>
                                                </td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo $subject['year_level']; ?></td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo getSemesterName($subject['semester']); ?></td>
                                                <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($subject['prerequisite'] ?: 'None'); ?></td>
                                                <td class="py-4 px-4">
                                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $subject['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                                        <?php echo ucfirst($subject['status']); ?>
                                                    </span>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <div class="flex space-x-2">
                                                        <button type="button" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors"
                                                                onclick="editSubject(<?php echo htmlspecialchars(json_encode($subject)); ?>)">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="bg-red-100 text-red-700 px-3 py-1 rounded-lg text-sm hover:bg-red-200 transition-colors"
                                                                onclick="deleteSubject(<?php echo $subject['id']; ?>, '<?php echo htmlspecialchars($subject['subject_code']); ?>')">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<!-- Edit Subject Modal -->
<div id="editModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Edit Subject</h3>
                    <button onclick="closeEditModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form method="POST" action="" id="editForm">
                    <input type="hidden" name="action" value="edit">
                    <input type="hidden" name="subject_id" id="editSubjectId">

                    <div class="space-y-4">
                        <div>
                            <label for="editSubjectCodeInput" class="block text-sm font-medium text-gray-700 mb-2">Subject Code</label>
                            <input type="text" id="editSubjectCodeInput" name="subject_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                        </div>

                        <div>
                            <label for="editSubjectNameInput" class="block text-sm font-medium text-gray-700 mb-2">Subject Name</label>
                            <input type="text" id="editSubjectNameInput" name="subject_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="editSubjectUnitsInput" class="block text-sm font-medium text-gray-700 mb-2">Units</label>
                                <input type="number" id="editSubjectUnitsInput" name="units" min="1" max="10" required
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                            </div>
                            <div>
                                <label for="editSubjectStatusInput" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                <select id="editSubjectStatusInput" name="status" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="editSubjectCourseInput" class="block text-sm font-medium text-gray-700 mb-2">Course</label>
                            <select id="editSubjectCourseInput" name="course_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                <option value="">Select Course</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>"><?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="editSubjectYearInput" class="block text-sm font-medium text-gray-700 mb-2">Year Level</label>
                                <select id="editSubjectYearInput" name="year_level" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                    <option value="">Select Year</option>
                                    <option value="1">1st Year</option>
                                    <option value="2">2nd Year</option>
                                    <option value="3">3rd Year</option>
                                    <option value="4">4th Year</option>
                                </select>
                            </div>
                            <div>
                                <label for="editSubjectSemesterInput" class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                                <select id="editSubjectSemesterInput" name="semester" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                    <option value="">Select Semester</option>
                                    <option value="1">1st Semester</option>
                                    <option value="2">2nd Semester</option>
                                    <option value="3">Summer</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="editSubjectPrereqInput" class="block text-sm font-medium text-gray-700 mb-2">Prerequisite</label>
                            <input type="text" id="editSubjectPrereqInput" name="prerequisite"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., MATH101, CS101 (optional)">
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-save mr-2"></i>Update Subject
                        </button>
                        <button type="button" onclick="closeEditModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Delete Subject</h3>
                    <button onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                    </div>
                    <div>
                        <h4 class="text-lg font-semibold text-gray-900">Are you sure?</h4>
                        <p class="text-gray-600">This action cannot be undone.</p>
                    </div>
                </div>
                <p class="text-gray-700 mb-6">
                    You are about to delete subject: <span id="deleteSubjectCode" class="font-semibold"></span>
                </p>
                <form id="deleteForm" method="POST">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="subject_id" id="deleteSubjectId">
                    <div class="flex space-x-3">
                        <button type="submit" class="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                            Delete Subject
                        </button>
                        <button type="button" onclick="closeDeleteModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Add Subject Modal -->
<div id="addModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-900">Add New Subject</h3>
                    <button onclick="closeAddModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <form method="POST" action="" id="addForm">
                    <input type="hidden" name="action" value="add">

                    <div class="space-y-4">
                        <div>
                            <label for="addSubjectCode" class="block text-sm font-medium text-gray-700 mb-2">Subject Code *</label>
                            <input type="text" id="addSubjectCode" name="subject_code" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., MATH101, CS101">
                        </div>

                        <div>
                            <label for="addSubjectName" class="block text-sm font-medium text-gray-700 mb-2">Subject Name *</label>
                            <input type="text" id="addSubjectName" name="subject_name" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., College Algebra, Introduction to Programming">
                        </div>

                        <div>
                            <label for="addSubjectUnits" class="block text-sm font-medium text-gray-700 mb-2">Units *</label>
                            <input type="number" id="addSubjectUnits" name="units" min="1" max="10" required
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="3">
                        </div>

                        <div>
                            <label for="addSubjectCourse" class="block text-sm font-medium text-gray-700 mb-2">Course *</label>
                            <select id="addSubjectCourse" name="course_id" required
                                    class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                <option value="">Select Course</option>
                                <?php foreach ($courses as $course): ?>
                                    <option value="<?php echo $course['id']; ?>"><?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?></option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label for="addSubjectYear" class="block text-sm font-medium text-gray-700 mb-2">Year Level *</label>
                                <select id="addSubjectYear" name="year_level" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                    <option value="">Select Year</option>
                                    <option value="1">1st Year</option>
                                    <option value="2">2nd Year</option>
                                    <option value="3">3rd Year</option>
                                    <option value="4">4th Year</option>
                                </select>
                            </div>
                            <div>
                                <label for="addSubjectSemester" class="block text-sm font-medium text-gray-700 mb-2">Semester *</label>
                                <select id="addSubjectSemester" name="semester" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent">
                                    <option value="">Select Semester</option>
                                    <option value="1">1st Semester</option>
                                    <option value="2">2nd Semester</option>
                                    <option value="3">Summer</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label for="addSubjectPrereq" class="block text-sm font-medium text-gray-700 mb-2">Prerequisite</label>
                            <input type="text" id="addSubjectPrereq" name="prerequisite"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent"
                                   placeholder="e.g., MATH101, CS101 (optional)">
                        </div>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>Add Subject
                        </button>
                        <button type="button" onclick="closeAddModal()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function editSubject(subject) {
    const modal = document.getElementById('editModal');

    // Populate form fields
    document.getElementById('editSubjectId').value = subject.id;
    document.getElementById('editSubjectCodeInput').value = subject.subject_code;
    document.getElementById('editSubjectNameInput').value = subject.subject_name;
    document.getElementById('editSubjectUnitsInput').value = subject.units;
    document.getElementById('editSubjectStatusInput').value = subject.status;
    document.getElementById('editSubjectCourseInput').value = subject.course_id;
    document.getElementById('editSubjectYearInput').value = subject.year_level;
    document.getElementById('editSubjectSemesterInput').value = subject.semester;
    document.getElementById('editSubjectPrereqInput').value = subject.prerequisite || '';

    modal.classList.remove('hidden');
}

function closeEditModal() {
    document.getElementById('editModal').classList.add('hidden');
}

function deleteSubject(subjectId, subjectCode) {
    const modal = document.getElementById('deleteModal');

    document.getElementById('deleteSubjectId').value = subjectId;
    document.getElementById('deleteSubjectCode').textContent = subjectCode;

    modal.classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

function openAddModal() {
    document.getElementById('addModal').classList.remove('hidden');
}

function closeAddModal() {
    document.getElementById('addModal').classList.add('hidden');
}

// Add loading states for edit and add forms
document.addEventListener('DOMContentLoaded', function() {
    // Handle edit form submission
    const editForm = document.getElementById('editForm');
    if (editForm) {
        editForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Updating...';
                submitBtn.disabled = true;
            }
        });
    }

    // Handle add form submission
    const addForm = document.getElementById('addForm');
    if (addForm) {
        addForm.addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding...';
                submitBtn.disabled = true;
            }
        });
    }
});

// Close modals when clicking outside
document.addEventListener('click', function(e) {
    const editModal = document.getElementById('editModal');
    const deleteModal = document.getElementById('deleteModal');
    const addModal = document.getElementById('addModal');

    if (e.target === editModal) {
        closeEditModal();
    }
    if (e.target === deleteModal) {
        closeDeleteModal();
    }
    if (e.target === addModal) {
        closeAddModal();
    }
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
