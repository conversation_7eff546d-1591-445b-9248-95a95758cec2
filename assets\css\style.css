/* Masbate Colleges Online Enrollment System - Custom Styles */

:root {
    --nsc-primary: #1e3a8a;
    --nsc-secondary: #3b82f6;
    --nsc-accent: #fbbf24;
    --nsc-success: #10b981;
    --nsc-danger: #ef4444;
    --nsc-warning: #f59e0b;
    --nsc-info: #06b6d4;
    --nsc-light: #f8fafc;
    --nsc-dark: #1e293b;
}

/* Global Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
}

/* Custom Button Styles */
.btn-nsc-primary {
    background-color: var(--nsc-primary);
    border-color: var(--nsc-primary);
    color: white;
    transition: all 0.3s ease;
}

.btn-nsc-primary:hover {
    background-color: var(--nsc-secondary);
    border-color: var(--nsc-secondary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.btn-nsc-secondary {
    background-color: var(--nsc-secondary);
    border-color: var(--nsc-secondary);
    color: white;
}

.btn-nsc-secondary:hover {
    background-color: var(--nsc-primary);
    border-color: var(--nsc-primary);
    color: white;
}

/* Text Colors */
.text-nsc-primary { color: var(--nsc-primary) !important; }
.text-nsc-secondary { color: var(--nsc-secondary) !important; }
.text-nsc-accent { color: var(--nsc-accent) !important; }
.text-nsc-success { color: var(--nsc-success) !important; }
.text-nsc-danger { color: var(--nsc-danger) !important; }

/* Background Colors */
.bg-nsc-primary { background-color: var(--nsc-primary) !important; }
.bg-nsc-secondary { background-color: var(--nsc-secondary) !important; }
.bg-nsc-accent { background-color: var(--nsc-accent) !important; }
.bg-nsc-light { background-color: var(--nsc-light) !important; }

/* Card Styles */
.card {
    border: none;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.card-header-nsc {
    background-color: var(--nsc-primary);
    color: white;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    border: none;
    padding: 1rem 1.5rem;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Sidebar Styles */
.sidebar {
    background-color: #f8f9fa;
    min-height: calc(100vh - 56px);
    border-right: 1px solid #dee2e6;
}

.sidebar .nav-link {
    color: var(--nsc-primary);
    padding: 0.75rem 1rem;
    border-radius: 0.5rem;
    margin-bottom: 0.25rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .nav-link:hover,
.sidebar .nav-link.active {
    background-color: var(--nsc-primary);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link i {
    width: 20px;
    text-align: center;
}

/* Main Content */
.main-content {
    padding: 2rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: 600;
}

.status-pending {
    background-color: #fef3c7;
    color: #92400e;
}

.status-approved {
    background-color: #d1fae5;
    color: #065f46;
}

.status-returned {
    background-color: #fee2e2;
    color: #991b1b;
}

/* Enrollment Cards */
.enrollment-card {
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
}

.enrollment-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: var(--nsc-primary);
}

/* Hero Section */
.hero-section {
    background: linear-gradient(135deg, var(--nsc-primary) 0%, var(--nsc-secondary) 100%);
    min-height: 60vh;
    display: flex;
    align-items: center;
}

.min-vh-50 {
    min-height: 50vh;
}

/* Process Section */
.process-icon {
    font-size: 2rem;
    transition: all 0.3s ease;
}

.process-icon:hover {
    transform: scale(1.1);
}

/* Form Styles */
.form-control:focus {
    border-color: var(--nsc-primary);
    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
}

.form-select:focus {
    border-color: var(--nsc-primary);
    box-shadow: 0 0 0 0.2rem rgba(30, 58, 138, 0.25);
}

/* Table Styles */
.table {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table thead th {
    background-color: var(--nsc-light);
    border: none;
    font-weight: 600;
    color: var(--nsc-dark);
}

.table tbody tr:hover {
    background-color: rgba(30, 58, 138, 0.05);
}

/* Alert Styles */
.alert {
    border: none;
    border-radius: 0.75rem;
    border-left: 4px solid;
}

.alert-info {
    border-left-color: var(--nsc-info);
    background-color: rgba(6, 182, 212, 0.1);
}

.alert-success {
    border-left-color: var(--nsc-success);
    background-color: rgba(16, 185, 129, 0.1);
}

.alert-warning {
    border-left-color: var(--nsc-warning);
    background-color: rgba(245, 158, 11, 0.1);
}

.alert-danger {
    border-left-color: var(--nsc-danger);
    background-color: rgba(239, 68, 68, 0.1);
}

/* Footer Styles */
.footer {
    background: linear-gradient(135deg, var(--nsc-primary) 0%, var(--nsc-dark) 100%);
    color: white;
    padding: 3rem 0 2rem;
    margin-top: auto;
}

.footer a {
    transition: color 0.3s ease;
}

.footer a:hover {
    color: var(--nsc-accent) !important;
}

/* Loading Animation */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    border-radius: 0.25rem;
    background-color: #e5e7eb;
}

.progress-bar {
    border-radius: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .hero-section {
        min-height: 40vh;
        text-align: center;
    }
    
    .hero-section .display-4 {
        font-size: 2rem;
    }
    
    .sidebar {
        min-height: auto;
    }
    
    .enrollment-card:hover {
        transform: none;
    }
}

@media (max-width: 576px) {
    .hero-section .display-4 {
        font-size: 1.75rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn-lg {
        padding: 0.75rem 1.5rem;
        font-size: 1rem;
    }
}

/* Print Styles */
@media print {
    .sidebar,
    .navbar,
    .footer,
    .btn,
    .alert {
        display: none !important;
    }
    
    .main-content {
        padding: 0;
        margin: 0;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .card-header-nsc {
        background-color: #000 !important;
        color: #fff !important;
    }
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Utility Classes */
.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

.border-0 {
    border: 0 !important;
}

.rounded-lg {
    border-radius: 0.75rem !important;
}

.text-decoration-none {
    text-decoration: none !important;
}

/* File Upload Styles */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: var(--nsc-primary);
    background-color: rgba(30, 58, 138, 0.05);
}

.file-upload-area.dragover {
    border-color: var(--nsc-primary);
    background-color: rgba(30, 58, 138, 0.1);
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: var(--nsc-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--nsc-secondary);
}
