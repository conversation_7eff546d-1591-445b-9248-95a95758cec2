<?php
// Debug script to test why actions are not working
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Actions - Deep Examination</h1>";
echo "<p>Testing all components step by step...</p>";

// Test 1: Session
echo "<h2>1. Session Test</h2>";
session_start();
if (session_status() == PHP_SESSION_ACTIVE) {
    echo "✅ Session is active<br>";
    echo "Session ID: " . session_id() . "<br>";
} else {
    echo "❌ Session failed to start<br>";
}

// Test 2: File includes
echo "<h2>2. File Include Test</h2>";
try {
    require_once '../config/database.php';
    echo "✅ Database config loaded<br>";
} catch (Exception $e) {
    echo "❌ Database config failed: " . $e->getMessage() . "<br>";
}

try {
    require_once '../includes/functions.php';
    echo "✅ Functions loaded<br>";
} catch (Exception $e) {
    echo "❌ Functions failed: " . $e->getMessage() . "<br>";
}

// Test 3: Database connection
echo "<h2>3. Database Connection Test</h2>";
try {
    $db = new Database();
    $conn = $db->getConnection();
    if ($conn) {
        echo "✅ Database connection successful<br>";
        
        // Test query
        $stmt = $conn->query("SELECT 1 as test");
        $result = $stmt->fetch();
        if ($result['test'] == 1) {
            echo "✅ Database query test passed<br>";
        } else {
            echo "❌ Database query test failed<br>";
        }
    } else {
        echo "❌ Database connection failed<br>";
    }
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "<br>";
}

// Test 4: Check if tables exist
echo "<h2>4. Table Existence Test</h2>";
$required_tables = ['users', 'student_grades', 'enrollments', 'payments', 'subjects', 'courses'];
foreach ($required_tables as $table) {
    try {
        $stmt = $conn->query("SHOW TABLES LIKE '$table'");
        if ($stmt->rowCount() > 0) {
            echo "✅ Table '$table' exists<br>";
            
            // Check record count
            $count_stmt = $conn->query("SELECT COUNT(*) as count FROM $table");
            $count = $count_stmt->fetch()['count'];
            echo "&nbsp;&nbsp;&nbsp;Records: $count<br>";
        } else {
            echo "❌ Table '$table' missing<br>";
        }
    } catch (Exception $e) {
        echo "❌ Error checking table '$table': " . $e->getMessage() . "<br>";
    }
}

// Test 5: Check required columns in enrollments
echo "<h2>5. Enrollments Table Structure Test</h2>";
try {
    $stmt = $conn->query("DESCRIBE enrollments");
    $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $required_columns = ['verification_status', 'verification_notes', 'verified_at', 'verified_by'];
    
    $existing_columns = array_column($columns, 'Field');
    
    foreach ($required_columns as $col) {
        if (in_array($col, $existing_columns)) {
            echo "✅ Column '$col' exists<br>";
        } else {
            echo "❌ Column '$col' missing<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking enrollments structure: " . $e->getMessage() . "<br>";
}

// Test 6: Simulate POST action
echo "<h2>6. POST Action Simulation Test</h2>";

// Simulate admin session
$_SESSION['user_id'] = 1;
$_SESSION['user_type'] = 'admin';

echo "Simulating add_grade action...<br>";
try {
    // Check if we have sample data
    $users_stmt = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'");
    $user_count = $users_stmt->fetch()['count'];
    
    $subjects_stmt = $conn->query("SELECT COUNT(*) as count FROM subjects");
    $subject_count = $subjects_stmt->fetch()['count'];
    
    if ($user_count > 0 && $subject_count > 0) {
        echo "✅ Sample data available (Students: $user_count, Subjects: $subject_count)<br>";
        
        // Try to insert a test grade
        $stmt = $conn->prepare("INSERT INTO student_grades (student_id, subject_id, semester, school_year, grade, remarks, created_at) VALUES (?, ?, ?, ?, ?, ?, NOW())");
        $test_result = $stmt->execute([1, 1, 1, '2024-2025', 85.5, 'PASSED']);
        
        if ($test_result) {
            echo "✅ Test grade insertion successful<br>";
            
            // Clean up test data
            $conn->query("DELETE FROM student_grades WHERE grade = 85.5 AND remarks = 'PASSED' AND school_year = '2024-2025'");
            echo "✅ Test data cleaned up<br>";
        } else {
            echo "❌ Test grade insertion failed<br>";
        }
    } else {
        echo "❌ Insufficient sample data (Students: $user_count, Subjects: $subject_count)<br>";
    }
} catch (Exception $e) {
    echo "❌ POST simulation error: " . $e->getMessage() . "<br>";
}

// Test 7: Check PHP configuration
echo "<h2>7. PHP Configuration Test</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";

// Test 8: Check if headers can be sent
echo "<h2>8. Header Test</h2>";
if (!headers_sent()) {
    echo "✅ Headers not sent yet - redirects should work<br>";
} else {
    echo "❌ Headers already sent - redirects will fail<br>";
}

// Test 9: Test actual form submission
echo "<h2>9. Form Submission Test</h2>";
?>

<form method="POST" action="debug_actions.php">
    <input type="hidden" name="test_action" value="test_submit">
    <button type="submit" style="background: #16a34a; color: white; padding: 10px 20px; border: none; border-radius: 5px;">
        Test Form Submission
    </button>
</form>

<?php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_action'])) {
    echo "<div style='background: #dcfce7; border: 1px solid #16a34a; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✅ Form submission received successfully!<br>";
    echo "POST data: " . print_r($_POST, true) . "<br>";
    echo "This proves POST requests are working.<br>";
    echo "</div>";
}

echo "<h2>10. Action Links Test</h2>";
echo "<p>Test these links to see if they cause loading issues:</p>";
echo "<a href='debug_actions.php?action=test_get' style='background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 5px;'>Test GET Action</a><br><br>";

if (isset($_GET['action']) && $_GET['action'] === 'test_get') {
    echo "<div style='background: #dbeafe; border: 1px solid #3b82f6; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✅ GET action received successfully!<br>";
    echo "GET data: " . print_r($_GET, true) . "<br>";
    echo "This proves GET requests are working.<br>";
    echo "</div>";
}

echo "<h2>11. Redirect Test</h2>";
echo "<a href='debug_actions.php?test_redirect=1' style='background: #dc2626; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Test Redirect</a><br><br>";

if (isset($_GET['test_redirect']) && !headers_sent()) {
    echo "Attempting redirect...<br>";
    header('Location: debug_actions.php?redirected=1');
    exit();
}

if (isset($_GET['redirected'])) {
    echo "<div style='background: #fef3c7; border: 1px solid #f59e0b; padding: 10px; margin: 10px 0; border-radius: 5px;'>";
    echo "✅ Redirect test successful!<br>";
    echo "You were redirected to this page.<br>";
    echo "</div>";
}

echo "<h2>Summary</h2>";
echo "<p>If all tests above pass, the issue might be:</p>";
echo "<ul>";
echo "<li>JavaScript conflicts</li>";
echo "<li>Browser caching issues</li>";
echo "<li>XAMPP/Apache configuration</li>";
echo "<li>Specific form validation issues</li>";
echo "</ul>";

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Check browser console for JavaScript errors</li>";
echo "<li>Clear browser cache and cookies</li>";
echo "<li>Test in incognito/private mode</li>";
echo "<li>Check XAMPP error logs</li>";
echo "</ol>";

echo "<p><a href='records.php' style='background: #16a34a; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Go to Records Page</a></p>";
?>
