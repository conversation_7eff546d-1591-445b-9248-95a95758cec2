<?php
session_start();

// Check if system is installed
if (!file_exists('database_initialized.flag')) {
    header('Location: install.php');
    exit();
}

require_once 'config/database.php';
require_once 'includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isAdmin()) {
        header('Location: admin/dashboard.php');
    } else {
        header('Location: student/dashboard.php');
    }
    exit();
}

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $login_id = sanitizeInput($_POST['login_id']);
    $password = $_POST['password'];
    
    if (empty($login_id) || empty($password)) {
        $error_message = 'Please fill in all fields.';
    } else {
        $db = new Database();
        $conn = $db->getConnection();
        
        // Check if login_id is email or student_id
        $stmt = $conn->prepare("SELECT * FROM users WHERE (email = ? OR student_id = ?) AND status = 'active'");
        $stmt->execute([$login_id, $login_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($user && password_verify($password, $user['password'])) {
            // Set session variables
            $_SESSION['user_id'] = $user['id'];
            $_SESSION['student_id'] = $user['student_id'];
            $_SESSION['email'] = $user['email'];
            $_SESSION['first_name'] = $user['first_name'];
            $_SESSION['last_name'] = $user['last_name'];
            $_SESSION['user_type'] = $user['user_type'];
            $_SESSION['course_id'] = $user['course_id'];
            $_SESSION['year_level'] = $user['year_level'];
            
            // Log activity
            logActivity($user['id'], 'login', 'User logged in');
            
            // Redirect based on user type
            if ($user['user_type'] === 'admin') {
                header('Location: admin/dashboard.php');
            } else {
                header('Location: student/dashboard.php');
            }
            exit();
        } else {
            $error_message = 'Invalid credentials or account is inactive.';
        }
    }
}

$page_title = "Login";
?>

<?php include 'includes/header.php'; ?>

<div class="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md mx-auto">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center mx-auto mb-4 p-3 shadow-lg">
                <img src="assets/logo-school.png" alt="Masbate Colleges Logo" class="w-full h-full object-cover rounded-full">
            </div>
            <h2 class="text-3xl font-bold text-gray-900">Welcome Back</h2>
            <p class="text-gray-600 mt-2">Sign in to your account to continue</p>
        </div>

        <!-- Login Form -->
        <div class="bg-white rounded-2xl shadow-xl p-8 card-hover">
            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <form method="POST" action="" id="loginForm" class="space-y-6">
                <!-- Student ID/Email Field -->
                <div>
                    <label for="login_id" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-user mr-2 text-nsc-primary"></i>Student ID or Email
                    </label>
                    <input type="text"
                           id="login_id"
                           name="login_id"
                           required
                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                           placeholder="Enter your Student ID or Email"
                           value="<?php echo isset($_POST['login_id']) ? htmlspecialchars($_POST['login_id']) : ''; ?>">
                </div>

                <!-- Password Field -->
                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                        <i class="fas fa-lock mr-2 text-nsc-primary"></i>Password
                    </label>
                    <div class="relative">
                        <input type="password"
                               id="password"
                               name="password"
                               required
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors pr-12"
                               placeholder="Enter your password">
                        <button type="button"
                                id="togglePassword"
                                class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 transition-colors">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <!-- Remember Me -->
                <div class="flex items-center">
                    <input type="checkbox"
                           id="remember_me"
                           name="remember_me"
                           class="h-4 w-4 text-nsc-primary focus:ring-nsc-primary border-gray-300 rounded">
                    <label for="remember_me" class="ml-2 block text-sm text-gray-700">
                        Remember me
                    </label>
                </div>

                <!-- Submit Button -->
                <button type="submit"
                        class="w-full bg-gradient-to-r from-nsc-primary to-nsc-secondary text-white py-3 px-4 rounded-lg font-semibold hover:from-nsc-secondary hover:to-nsc-primary transition-all duration-300 transform hover:scale-105 btn-hover">
                    <i class="fas fa-sign-in-alt mr-2"></i>Sign In
                </button>
            </form>

            <!-- Divider -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <div class="text-center">
                    <p class="text-gray-600 mb-4">Don't have an account?</p>
                    <a href="register.php"
                       class="inline-flex items-center px-6 py-3 border border-nsc-primary text-nsc-primary rounded-lg hover:bg-nsc-primary hover:text-white transition-all duration-300">
                        <i class="fas fa-user-plus mr-2"></i>Create Account
                    </a>
                </div>

                <div class="text-center mt-4">
                    <a href="#" class="text-nsc-primary hover:text-nsc-secondary transition-colors text-sm">
                        <i class="fas fa-question-circle mr-1"></i>Forgot Password?
                    </a>
                </div>
            </div>
        </div>


    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    const togglePassword = document.getElementById('togglePassword');
    const passwordField = document.getElementById('password');

    if (togglePassword && passwordField) {
        togglePassword.addEventListener('click', function() {
            const type = passwordField.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordField.setAttribute('type', type);

            const icon = this.querySelector('i');
            icon.classList.toggle('fa-eye');
            icon.classList.toggle('fa-eye-slash');
        });
    }

    // Form validation with modern styling
    const form = document.getElementById('loginForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            const loginId = document.getElementById('login_id').value.trim();
            const password = document.getElementById('password').value;

            if (!loginId || !password) {
                e.preventDefault();
                showAlert('Please fill in all fields.', 'error');
                return false;
            }

            // Show loading state
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                showLoading(submitBtn);
            }
        });

        // Real-time validation
        const inputs = form.querySelectorAll('input[required]');
        inputs.forEach(input => {
            input.addEventListener('blur', function() {
                if (!this.value.trim()) {
                    this.classList.add('border-red-500', 'bg-red-50');
                    this.classList.remove('border-gray-300');
                } else {
                    this.classList.remove('border-red-500', 'bg-red-50');
                    this.classList.add('border-gray-300');
                }
            });

            input.addEventListener('input', function() {
                if (this.value.trim()) {
                    this.classList.remove('border-red-500', 'bg-red-50');
                    this.classList.add('border-gray-300');
                }
            });
        });
    }

    // Add smooth animations
    const formElements = document.querySelectorAll('.card-hover, input, button');
    formElements.forEach(element => {
        element.style.transition = 'all 0.3s ease';
    });
});
</script>

<?php include 'includes/footer.php'; ?>
