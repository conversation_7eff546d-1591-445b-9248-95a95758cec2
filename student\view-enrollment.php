<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get enrollment ID
$enrollment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

// Verify enrollment belongs to current user
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.student_id, c.course_code, c.course_name 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE e.id = ? AND e.student_id = ?");
$stmt->execute([$enrollment_id, $_SESSION['user_id']]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: enrollments.php');
    exit();
}

// Get selected subjects
$stmt = $conn->prepare("SELECT s.* FROM subjects s 
                       JOIN enrollment_subjects es ON s.id = es.subject_id 
                       WHERE es.enrollment_id = ? 
                       ORDER BY s.subject_code");
$stmt->execute([$enrollment_id]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Enrollment Details";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Fix sticky hover states */
        nav a {
            -webkit-tap-highlight-color: transparent;
        }
        nav a:focus {
            outline: none;
        }
        nav a:active {
            transform: none;
        }

        /* Ensure proper state transitions */
        nav a:not(.active):hover {
            background-color: rgba(255, 255, 255, 0.1) !important;
        }
        nav a:not(.active):not(:hover) {
            background-color: transparent !important;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group focus:outline-none">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-list mr-3 text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-eye text-2xl text-green-600"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Enrollment Details</h1>
                                <p class="text-gray-600">Review your enrollment details and status</p>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <?php if ($enrollment['status'] === 'approved'): ?>
                                <a href="download-cor.php?id=<?php echo $enrollment['id']; ?>" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                    <i class="fas fa-download mr-2"></i>Download COR
                                </a>
                            <?php endif; ?>
                            <a href="enrollments.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <i class="fas fa-arrow-left mr-2"></i>Back to Enrollments
                            </a>
                        </div>
                    </div>
                </div>

            <!-- Enrollment Status Alert -->
            <div class="mb-8">
                <?php
                $alert_colors = [
                    'approved' => 'bg-green-50 border-green-200 text-green-800',
                    'pending' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
                    'returned' => 'bg-red-50 border-red-200 text-red-800',
                    'draft' => 'bg-blue-50 border-blue-200 text-blue-800'
                ];

                $alert_icons = [
                    'approved' => 'fas fa-check-circle text-green-600',
                    'pending' => 'fas fa-clock text-yellow-600',
                    'returned' => 'fas fa-times-circle text-red-600',
                    'draft' => 'fas fa-edit text-blue-600'
                ];

                $alert_messages = [
                    'approved' => 'Your enrollment has been approved! You can now download your Certificate of Registration.',
                    'pending' => 'Your enrollment is pending approval by the registrar. Please wait for further updates.',
                    'returned' => 'Your enrollment has been returned. Please check the remarks below and resubmit if necessary.',
                    'draft' => 'Your enrollment is incomplete. Please upload payment proof to submit for registrar approval.'
                ];

                $status = $enrollment['status'];
                $alert_class = $alert_colors[$status] ?? 'bg-gray-50 border-gray-200 text-gray-800';
                $alert_icon = $alert_icons[$status] ?? 'fas fa-info-circle text-gray-600';
                $alert_message = $alert_messages[$status] ?? 'Unknown status';
                ?>

                <div class="border-l-4 p-6 rounded-r-lg <?php echo $alert_class; ?>">
                    <div class="flex items-start">
                        <i class="<?php echo $alert_icon; ?> mr-3 mt-1"></i>
                        <div>
                            <h3 class="font-bold text-lg mb-2">Status: <?php echo ucfirst($enrollment['status']); ?></h3>
                            <p class="mb-0"><?php echo $alert_message; ?></p>

                            <?php if ($enrollment['status'] === 'draft'): ?>
                                <div class="mt-4 p-4 bg-white bg-opacity-70 rounded-lg border border-blue-300">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <p class="font-semibold text-blue-900 mb-1">Next Step:</p>
                                            <p class="text-blue-800 text-sm">Upload payment proof to complete your enrollment</p>
                                        </div>
                                        <a href="payment-online.php?id=<?php echo $enrollment['id']; ?>" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium text-sm">
                                            <i class="fas fa-arrow-right mr-1"></i>Continue
                                        </a>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <?php if ($enrollment['remarks']): ?>
                                <div class="mt-4 p-3 bg-white bg-opacity-50 rounded-lg">
                                    <p class="font-semibold mb-1">Remarks:</p>
                                    <p class="mb-0"><?php echo htmlspecialchars($enrollment['remarks']); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Enrollment Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-2xl shadow-lg card-hover">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Enrollment Information</h2>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Enrollment ID:</span>
                            <span class="text-gray-900">#<?php echo str_pad($enrollment['id'], 6, '0', STR_PAD_LEFT); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Period:</span>
                            <span class="text-gray-900">
                                <?php echo getSemesterName($enrollment['semester']); ?> -
                                <?php echo $enrollment['school_year']; ?>
                            </span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Total Units:</span>
                            <span class="text-gray-900"><?php echo $enrollment['total_units']; ?> units</span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Total Fees:</span>
                            <span class="font-bold text-green-600 text-lg"><?php echo formatCurrency($enrollment['total_fees']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Submitted:</span>
                            <span class="text-gray-900"><?php echo date('M d, Y g:i A', strtotime($enrollment['submitted_at'])); ?></span>
                        </div>

                        <?php if ($enrollment['status'] === 'approved' && $enrollment['approved_at']): ?>
                            <hr class="border-gray-200">
                            <div class="flex justify-between items-center">
                                <span class="font-semibold text-gray-700">Approved:</span>
                                <span class="text-green-600 font-medium"><?php echo date('M d, Y g:i A', strtotime($enrollment['approved_at'])); ?></span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="bg-white rounded-2xl shadow-lg card-hover">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Student Information</h2>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Student ID:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['student_id']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Name:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Course:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Subjects -->
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-book text-purple-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Selected Subjects</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700 w-20">Units</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Prerequisites</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700 w-24">Fee</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <?php
                                    $rate_per_unit = 500;
                                    foreach ($subjects as $subject):
                                    ?>
                                        <tr class="hover:bg-gray-50 transition-colors">
                                            <td class="py-4 px-4 font-semibold text-gray-900"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                            <td class="py-4 px-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <?php echo $subject['units']; ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4 text-gray-600"><?php echo htmlspecialchars($subject['prerequisite'] ?: 'None'); ?></td>
                                            <td class="py-4 px-4 font-medium text-gray-900"><?php echo formatCurrency($subject['units'] * $rate_per_unit); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="border-t-2 border-gray-300">
                                    <tr class="bg-blue-50">
                                        <td colspan="2" class="py-3 px-4 font-semibold text-gray-900">Subtotal (<?php echo count($subjects); ?> subjects)</td>
                                        <td class="py-3 px-4 font-semibold text-gray-900"><?php echo $enrollment['total_units']; ?> units</td>
                                        <td class="py-3 px-4"></td>
                                        <td class="py-3 px-4 font-semibold text-gray-900"><?php echo formatCurrency($enrollment['total_units'] * $rate_per_unit); ?></td>
                                    </tr>
                                    <tr class="bg-blue-50">
                                        <td colspan="4" class="py-3 px-4 font-semibold text-gray-900">Miscellaneous Fee</td>
                                        <td class="py-3 px-4 font-semibold text-gray-900"><?php echo formatCurrency(2000); ?></td>
                                    </tr>
                                    <tr class="bg-green-50">
                                        <td colspan="4" class="py-3 px-4 font-bold text-green-800">Total Amount</td>
                                        <td class="py-3 px-4 font-bold text-green-800 text-lg"><?php echo formatCurrency($enrollment['total_fees']); ?></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Proof -->
            <?php if ($enrollment['payment_proof']): ?>
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-receipt text-yellow-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Payment Proof</h2>
                        </div>
                    </div>
                    <div class="p-8 text-center">
                        <?php
                        $file_extension = strtolower(pathinfo($enrollment['payment_proof'], PATHINFO_EXTENSION));
                        $file_path = '../uploads/payments/' . $enrollment['payment_proof'];
                        ?>

                        <?php if (in_array($file_extension, ['jpg', 'jpeg', 'png'])): ?>
                            <div class="mb-6">
                                <img src="<?php echo $file_path; ?>" alt="Payment Proof" class="max-h-96 mx-auto rounded-lg shadow-md">
                            </div>
                        <?php elseif ($file_extension === 'pdf'): ?>
                            <div class="mb-6">
                                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-file-pdf text-3xl text-red-600"></i>
                                </div>
                                <p class="text-gray-600 font-medium">PDF Document</p>
                            </div>
                        <?php endif; ?>

                        <div class="flex justify-center space-x-4">
                            <a href="<?php echo $file_path; ?>" target="_blank" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium btn-hover">
                                <i class="fas fa-external-link-alt mr-2"></i>View Full Size
                            </a>
                            <a href="<?php echo $file_path; ?>" download class="border border-blue-300 text-blue-700 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors font-medium">
                                <i class="fas fa-download mr-2"></i>Download
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Action Buttons -->
            <div class="bg-white rounded-2xl shadow-lg">
                <div class="p-8 text-center">
                    <?php if ($enrollment['status'] === 'approved'): ?>
                        <div class="mb-6">
                            <a href="download-cor.php?id=<?php echo $enrollment['id']; ?>" class="bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg btn-hover">
                                <i class="fas fa-download mr-2"></i>Download Certificate of Registration
                            </a>
                        </div>
                    <?php elseif ($enrollment['status'] === 'pending'): ?>
                        <div class="mb-6">
                            <?php if (!$enrollment['payment_proof']): ?>
                                <!-- If pending but no payment proof, allow to continue payment -->
                                <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                                    <div class="flex items-center mb-3">
                                        <i class="fas fa-exclamation-circle text-orange-600 mr-2"></i>
                                        <span class="text-orange-800 font-medium">
                                            Payment proof is required to complete your enrollment.
                                        </span>
                                    </div>
                                    <a href="payment-online.php?id=<?php echo $enrollment['id']; ?>" class="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors font-medium inline-block">
                                        <i class="fas fa-upload mr-2"></i>Upload Payment Proof
                                    </a>
                                </div>
                            <?php else: ?>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 inline-block">
                                    <div class="flex items-center">
                                        <i class="fas fa-clock text-blue-600 mr-2"></i>
                                        <span class="text-blue-800 font-medium">
                                            Your enrollment is being reviewed. You will be notified once it's approved.
                                        </span>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php elseif ($enrollment['status'] === 'draft'): ?>
                        <div class="mb-6">
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-info-circle text-yellow-600 mr-2"></i>
                                    <span class="text-yellow-800 font-medium">
                                        Your enrollment is incomplete. Please upload payment proof to submit for approval.
                                    </span>
                                </div>
                                <a href="payment-online.php?id=<?php echo $enrollment['id']; ?>" class="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 transition-colors font-medium inline-block">
                                    <i class="fas fa-credit-card mr-2"></i>Continue to Payment
                                </a>
                            </div>
                        </div>
                    <?php elseif ($enrollment['status'] === 'returned'): ?>
                        <div class="mb-6">
                            <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                                <div class="flex items-center mb-3">
                                    <i class="fas fa-exclamation-triangle text-red-600 mr-2"></i>
                                    <span class="text-red-800 font-medium">
                                        Your enrollment was returned. Please review the remarks and resubmit if necessary.
                                    </span>
                                </div>
                                <a href="payment-online.php?id=<?php echo $enrollment['id']; ?>" class="bg-red-600 text-white px-6 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium inline-block">
                                    <i class="fas fa-redo mr-2"></i>Resubmit Payment
                                </a>
                            </div>
                        </div>
                    <?php endif; ?>

                    <div class="flex justify-center space-x-4">
                        <a href="enrollments.php" class="border border-blue-300 text-blue-700 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors font-medium">
                            <i class="fas fa-arrow-left mr-2"></i>Back to My Enrollments
                        </a>
                        <a href="dashboard.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            <i class="fas fa-home mr-2"></i>Dashboard
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn-hover');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 1000);
            }
        });
    });

    // Fix sticky hover states on navigation links
    const navLinks = document.querySelectorAll('nav a');
    navLinks.forEach(link => {
        // Remove focus after click to prevent sticky states
        link.addEventListener('click', function() {
            setTimeout(() => {
                this.blur();
            }, 100);
        });

        // Handle touch devices
        link.addEventListener('touchend', function() {
            setTimeout(() => {
                this.blur();
            }, 100);
        });

        // Clear any stuck hover states on mouse leave
        link.addEventListener('mouseleave', function() {
            if (!this.classList.contains('bg-white')) {
                this.style.backgroundColor = '';
                this.style.color = '';
            }
        });
    });
});
</script>

</body>
</html>
