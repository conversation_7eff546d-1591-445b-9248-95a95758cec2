<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get current semester and school year
$current_period = getCurrentSemesterYear();

// Handle export requests FIRST - before any output
if (isset($_GET['export'])) {
    try {
        $export_type = $_GET['export'];
        $format = $_GET['format'] ?? 'csv';

        // Get filter parameters
        $semester = $_GET['semester'] ?? $current_period['semester'];
        $school_year = $_GET['school_year'] ?? $current_period['school_year'];
        $status = $_GET['status'] ?? '';
        $course = $_GET['course'] ?? '';

        $data = [];
        $headers = [];
        $filename = '';

        // Build query based on export type
        if ($export_type === 'enrollments') {
            $where_conditions = ["e.semester = ?", "e.school_year = ?"];
            $params = [$semester, $school_year];

            if (!empty($status)) {
                $where_conditions[] = "e.status = ?";
                $params[] = $status;
            }

            if (!empty($course)) {
                $where_conditions[] = "u.course_id = ?";
                $params[] = $course;
            }

            $where_clause = "WHERE " . implode(" AND ", $where_conditions);

            $stmt = $conn->prepare("SELECT e.id, u.student_id, u.first_name, u.last_name, u.email,
                                   c.course_code, c.course_name, u.year_level, e.semester, e.school_year,
                                   e.total_units, e.total_fees, e.status, e.submitted_at, e.approved_at
                                   FROM enrollments e
                                   JOIN users u ON e.student_id = u.id
                                   LEFT JOIN courses c ON u.course_id = c.id
                                   $where_clause
                                   ORDER BY e.submitted_at DESC");
            $stmt->execute($params);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $filename = "enrollments_report_" . date('Y-m-d_H-i-s');
            $headers = ['Enrollment ID', 'Student ID', 'First Name', 'Last Name', 'Email', 'Course Code', 'Course Name', 'Year Level', 'Semester', 'School Year', 'Total Units', 'Total Fees', 'Status', 'Submitted At', 'Approved At'];

        } elseif ($export_type === 'students') {
            $where_conditions = ["u.user_type = 'student'"];
            $params = [];

            if (!empty($course)) {
                $where_conditions[] = "u.course_id = ?";
                $params[] = $course;
            }

            $where_clause = "WHERE " . implode(" AND ", $where_conditions);

            $stmt = $conn->prepare("SELECT u.student_id, u.first_name, u.last_name, u.middle_name, u.email,
                                   u.contact_number, u.address, c.course_code, c.course_name, u.year_level,
                                   u.status, u.created_at
                                   FROM users u
                                   LEFT JOIN courses c ON u.course_id = c.id
                                   $where_clause
                                   ORDER BY u.student_id");
            $stmt->execute($params);
            $data = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $filename = "students_report_" . date('Y-m-d_H-i-s');
            $headers = ['Student ID', 'First Name', 'Last Name', 'Middle Name', 'Email', 'Contact Number', 'Address', 'Course Code', 'Course Name', 'Year Level', 'Status', 'Created At'];
        } else {
            throw new Exception('Invalid export type');
        }

        // Check if data exists
        if (empty($data)) {
            throw new Exception('No data found for the selected criteria');
        }

        // Export as CSV
        if ($format === 'csv') {
            // Clear ALL output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers for CSV download
            header('Content-Type: text/csv; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.csv"');
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            $output = fopen('php://output', 'w');

            // Add BOM for UTF-8
            fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

            // Write headers
            fputcsv($output, $headers);

            // Write data
            foreach ($data as $row) {
                fputcsv($output, $row);
            }

            fclose($output);
            exit();
        }

        // Export as Excel (HTML table that Excel can read)
        elseif ($format === 'excel') {
            // Clear ALL output buffers
            while (ob_get_level()) {
                ob_end_clean();
            }

            // Set headers for Excel download
            header('Content-Type: application/vnd.ms-excel; charset=utf-8');
            header('Content-Disposition: attachment; filename="' . $filename . '.xls"');
            header('Cache-Control: no-cache, no-store, must-revalidate');
            header('Pragma: no-cache');
            header('Expires: 0');

            echo '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns="http://www.w3.org/TR/REC-html40">';
            echo '<head><meta charset="utf-8"><meta http-equiv="Content-Type" content="text/html; charset=utf-8"></head>';
            echo '<body>';
            echo '<table border="1">';
            echo '<tr>';
            foreach ($headers as $header) {
                echo '<th style="background-color: #f0f0f0; font-weight: bold;">' . htmlspecialchars($header) . '</th>';
            }
            echo '</tr>';

            foreach ($data as $row) {
                echo '<tr>';
                foreach ($row as $cell) {
                    echo '<td>' . htmlspecialchars($cell ?? '') . '</td>';
                }
                echo '</tr>';
            }
            echo '</table>';
            echo '</body></html>';
            exit();
        } else {
            throw new Exception('Invalid export format');
        }

    } catch (Exception $e) {
        // Log the error
        error_log("Export error: " . $e->getMessage());

        // Set error message in session and redirect
        $_SESSION['error_message'] = 'Export failed: ' . $e->getMessage();
        header('Location: reports.php');
        exit();
    }
}

// Get filter parameters for display
$semester_filter = $_GET['semester'] ?? $current_period['semester'];
$school_year_filter = $_GET['school_year'] ?? $current_period['school_year'];
$status_filter = $_GET['status'] ?? '';
$course_filter = $_GET['course'] ?? '';

// Get all courses for dropdown (ensure unique courses)
$stmt = $conn->prepare("SELECT * FROM courses WHERE status = 'active' GROUP BY course_code, course_name ORDER BY course_code");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get statistics
$stats = [];

// Total enrollments for current period
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE semester = ? AND school_year = ?");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$stats['current_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Approved enrollments for current period
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE status = 'approved' AND semester = ? AND school_year = ?");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$stats['approved_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Total students
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'");
$stmt->execute();
$stats['total_students'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Total revenue for current period
$stmt = $conn->prepare("SELECT SUM(total_fees) as total FROM enrollments WHERE status = 'approved' AND semester = ? AND school_year = ?");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$stats['total_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'] ?? 0;

$page_title = "Reports & Analytics";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Error/Success Messages -->
                <?php if (isset($_SESSION['error_message'])): ?>
                    <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <span><?php echo $_SESSION['error_message']; unset($_SESSION['error_message']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['success_message'])): ?>
                    <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-3"></i>
                            <span><?php echo $_SESSION['success_message']; unset($_SESSION['success_message']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-chart-bar text-2xl text-nsc-primary"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Reports & Analytics</h1>
                            <p class="text-gray-600">Generate and export enrollment reports</p>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                        <div class="w-16 h-16 bg-nsc-primary bg-opacity-10 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-list text-2xl text-nsc-primary"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-nsc-primary mb-2"><?php echo number_format($stats['current_enrollments']); ?></h3>
                        <p class="text-gray-600 font-medium">Current Period Enrollments</p>
                    </div>
                    <div class="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-green-600 mb-2"><?php echo number_format($stats['approved_enrollments']); ?></h3>
                        <p class="text-gray-600 font-medium">Approved Enrollments</p>
                    </div>
                    <div class="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-user-graduate text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-blue-600 mb-2"><?php echo number_format($stats['total_students']); ?></h3>
                        <p class="text-gray-600 font-medium">Total Students</p>
                    </div>
                    <div class="bg-white rounded-2xl shadow-lg p-6 text-center hover:shadow-xl transition-shadow">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-peso-sign text-2xl text-yellow-600"></i>
                        </div>
                        <h3 class="text-3xl font-bold text-yellow-600 mb-2"><?php echo formatCurrency($stats['total_revenue']); ?></h3>
                        <p class="text-gray-600 font-medium">Total Revenue</p>
                    </div>
                </div>

                <!-- Export Forms -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                    <!-- Enrollment Reports -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-file-export text-blue-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">Enrollment Reports</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="GET" action="" id="enrollmentReportForm" class="space-y-4">
                                <input type="hidden" name="export" value="enrollments">

                                <div>
                                    <label for="semester" class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                                    <select name="semester" id="semester" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="1" <?php echo $semester_filter == 1 ? 'selected' : ''; ?>>1st Semester</option>
                                        <option value="2" <?php echo $semester_filter == 2 ? 'selected' : ''; ?>>2nd Semester</option>
                                        <option value="3" <?php echo $semester_filter == 3 ? 'selected' : ''; ?>>Summer</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="school_year" class="block text-sm font-medium text-gray-700 mb-2">School Year</label>
                                    <input type="text" name="school_year" id="school_year" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors"
                                           value="<?php echo htmlspecialchars($school_year_filter); ?>"
                                           placeholder="e.g., 2023-2024">
                                </div>

                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                    <select name="status" id="status" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Status</option>
                                        <option value="pending" <?php echo $status_filter == 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo $status_filter == 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="returned" <?php echo $status_filter == 'returned' ? 'selected' : ''; ?>>Returned</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="course" class="block text-sm font-medium text-gray-700 mb-2">Course</label>
                                    <select name="course" id="course" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Courses</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['id']; ?>" <?php echo $course_filter == $course['id'] ? 'selected' : ''; ?>>
                                                <?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div>
                                    <label for="format" class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                                    <select name="format" id="format" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="csv">CSV</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>

                                <button type="submit" class="w-full bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                                    <i class="fas fa-download mr-2"></i>Export Enrollment Report
                                </button>
                            </form>
                        </div>
                    </div>

                    <!-- Student Reports -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-users text-green-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">Student Reports</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="GET" action="" id="studentReportForm" class="space-y-4">
                                <input type="hidden" name="export" value="students">

                                <div>
                                    <label for="student_course" class="block text-sm font-medium text-gray-700 mb-2">Course</label>
                                    <select name="course" id="student_course" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Courses</option>
                                        <?php foreach ($courses as $course): ?>
                                            <option value="<?php echo $course['id']; ?>">
                                                <?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>

                                <div>
                                    <label for="student_format" class="block text-sm font-medium text-gray-700 mb-2">Export Format</label>
                                    <select name="format" id="student_format" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="csv">CSV</option>
                                        <option value="excel">Excel</option>
                                    </select>
                                </div>

                                <button type="submit" class="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                    <i class="fas fa-download mr-2"></i>Export Student Report
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Quick Reports -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-bolt text-purple-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Quick Reports</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <a href="?export=enrollments&semester=<?php echo $current_period['semester']; ?>&school_year=<?php echo $current_period['school_year']; ?>&status=approved&format=csv"
                               class="bg-green-600 text-white px-6 py-4 rounded-lg hover:bg-green-700 transition-colors text-center font-medium">
                                <i class="fas fa-check mr-2"></i>Approved Enrollments (CSV)
                            </a>
                            <a href="?export=enrollments&semester=<?php echo $current_period['semester']; ?>&school_year=<?php echo $current_period['school_year']; ?>&status=pending&format=csv"
                               class="bg-yellow-600 text-white px-6 py-4 rounded-lg hover:bg-yellow-700 transition-colors text-center font-medium">
                                <i class="fas fa-clock mr-2"></i>Pending Enrollments (CSV)
                            </a>
                            <a href="?export=students&format=csv" class="bg-blue-600 text-white px-6 py-4 rounded-lg hover:bg-blue-700 transition-colors text-center font-medium">
                                <i class="fas fa-users mr-2"></i>All Students (CSV)
                            </a>
                            <a href="?export=enrollments&semester=<?php echo $current_period['semester']; ?>&school_year=<?php echo $current_period['school_year']; ?>&format=excel"
                               class="bg-nsc-primary text-white px-6 py-4 rounded-lg hover:bg-nsc-secondary transition-colors text-center font-medium">
                                <i class="fas fa-file-excel mr-2"></i>Current Period (Excel)
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Report Information -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-gray-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Report Information</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-lg font-semibold text-nsc-primary mb-4">Enrollment Reports Include:</h3>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Student information (ID, name, email)</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Course and year level details</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Enrollment period and status</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Total units and fees</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Submission and approval dates</span>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-nsc-primary mb-4">Student Reports Include:</h3>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Complete student profile</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Contact information</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Course enrollment details</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Account status and creation date</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Filterable by course</span>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<!-- No JavaScript - let forms submit naturally -->

<?php include '../includes/admin_layout_end.php'; ?>
