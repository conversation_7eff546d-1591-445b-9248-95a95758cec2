<?php
function generateOfficialEnrollmentList($conn) {
    // Get current semester and school year
    $current_period = getCurrentSemesterYear();
    
    // Get all verified enrollments for current period
    $enrollments_stmt = $conn->prepare("SELECT e.*, u.student_id, u.first_name, u.last_name, u.middle_name, u.email, u.phone, u.address, 
                                       c.course_code, c.course_name, v.first_name as verifier_first, v.last_name as verifier_last
                                       FROM enrollments e 
                                       JOIN users u ON e.student_id = u.id 
                                       LEFT JOIN courses c ON u.course_id = c.id 
                                       LEFT JOIN users v ON e.verified_by = v.id
                                       WHERE e.verification_status = 'verified' 
                                       AND e.semester = ? AND e.school_year = ?
                                       ORDER BY c.course_code, u.last_name, u.first_name");
    $enrollments_stmt->execute([$current_period['semester'], $current_period['school_year']]);
    $enrollments = $enrollments_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Group by course
    $courses = [];
    foreach ($enrollments as $enrollment) {
        $course_key = $enrollment['course_code'];
        if (!isset($courses[$course_key])) {
            $courses[$course_key] = [
                'course_name' => $enrollment['course_name'],
                'students' => []
            ];
        }
        $courses[$course_key]['students'][] = $enrollment;
    }
    
    // Generate HTML
    $html = generateOfficialListHTML($courses, $current_period);
    
    // Set headers
    header('Content-Type: text/html');
    header('Content-Disposition: inline; filename="official_enrollment_list_' . $current_period['school_year'] . '_' . $current_period['semester'] . '.html"');
    
    echo $html;
}

function generateOfficialListHTML($courses, $current_period) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Official Enrollment List - <?php echo $current_period['school_year']; ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #16a34a; padding-bottom: 20px; }
            .school-name { font-size: 24px; font-weight: bold; color: #16a34a; }
            .document-title { font-size: 18px; font-weight: bold; margin: 20px 0; }
            .course-section { margin: 30px 0; page-break-inside: avoid; }
            .course-header { background: #f8f9fa; padding: 15px; border-left: 4px solid #16a34a; margin-bottom: 15px; }
            .course-title { font-size: 16px; font-weight: bold; color: #16a34a; }
            .students-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
            .students-table th, .students-table td { border: 1px solid #ddd; padding: 8px; text-align: left; font-size: 12px; }
            .students-table th { background-color: #f8f9fa; font-weight: bold; }
            .summary { margin: 20px 0; background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .signature-section { margin-top: 50px; }
            @media print { 
                body { margin: 0; font-size: 12px; }
                .course-section { page-break-inside: avoid; }
            }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="school-name">MASBATE COLLEGES</div>
            <div>Masbate City, Masbate, Philippines</div>
            <div>Tel: (************* | Email: <EMAIL></div>
            <div class="document-title">OFFICIAL ENROLLMENT LIST</div>
            <div><?php echo getSemesterName($current_period['semester']); ?> - School Year <?php echo $current_period['school_year']; ?></div>
        </div>

        <?php 
        $total_students = 0;
        foreach ($courses as $course_code => $course_data): 
            $course_count = count($course_data['students']);
            $total_students += $course_count;
        ?>
        <div class="course-section">
            <div class="course-header">
                <div class="course-title"><?php echo htmlspecialchars($course_code . ' - ' . $course_data['course_name']); ?></div>
                <div style="font-size: 14px; color: #666;">Total Students: <?php echo $course_count; ?></div>
            </div>
            
            <table class="students-table">
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 15%;">Student ID</th>
                        <th style="width: 25%;">Full Name</th>
                        <th style="width: 20%;">Email</th>
                        <th style="width: 15%;">Phone</th>
                        <th style="width: 10%;">Units</th>
                        <th style="width: 10%;">Verified Date</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($course_data['students'] as $index => $student): ?>
                    <tr>
                        <td><?php echo $index + 1; ?></td>
                        <td><?php echo htmlspecialchars($student['student_id']); ?></td>
                        <td><?php echo htmlspecialchars($student['last_name'] . ', ' . $student['first_name'] . ' ' . $student['middle_name']); ?></td>
                        <td><?php echo htmlspecialchars($student['email']); ?></td>
                        <td><?php echo htmlspecialchars($student['phone']); ?></td>
                        <td><?php echo $student['total_units']; ?></td>
                        <td><?php echo $student['verified_at'] ? date('M d, Y', strtotime($student['verified_at'])) : 'N/A'; ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endforeach; ?>

        <div class="summary">
            <h3 style="margin: 0 0 15px 0; color: #16a34a;">Enrollment Summary</h3>
            <table style="width: 100%;">
                <tr>
                    <td><strong>Total Number of Courses:</strong></td>
                    <td><?php echo count($courses); ?></td>
                </tr>
                <tr>
                    <td><strong>Total Number of Students:</strong></td>
                    <td><?php echo $total_students; ?></td>
                </tr>
                <tr>
                    <td><strong>School Year:</strong></td>
                    <td><?php echo $current_period['school_year']; ?></td>
                </tr>
                <tr>
                    <td><strong>Semester:</strong></td>
                    <td><?php echo getSemesterName($current_period['semester']); ?></td>
                </tr>
                <tr>
                    <td><strong>Date Generated:</strong></td>
                    <td><?php echo date('F d, Y'); ?></td>
                </tr>
            </table>
        </div>

        <div class="signature-section">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Registrar</strong><br>
                            <small>Date: <?php echo date('F d, Y'); ?></small>
                        </div>
                    </td>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Academic Affairs Director</strong><br>
                            <small>Date: _______________</small>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666;">
            This is an official document generated by the Masbate Colleges Student Information System.<br>
            Registrar's Office - Masbate Colleges
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
