<?php
// Determine the base URL for navigation links
function getBaseUrl() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script);

    // Remove any subdirectories to get to the root of the application
    $pathParts = explode('/', trim($path, '/'));
    $basePath = '';

    // Find the application root (where index.php is located)
    foreach ($pathParts as $part) {
        if ($part === 'admin' || $part === 'student' || $part === 'includes') {
            break;
        }
        if (!empty($part)) {
            $basePath .= '/' . $part;
        }
    }

    return $protocol . '://' . $host . $basePath;
}

$base_url = getBaseUrl();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?>Masbate Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#16a34a',
                        'nsc-secondary': '#22c55e',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-in': 'slideIn 0.5s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(10px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideIn: {
                            '0%': { opacity: '0', transform: 'translateX(-10px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #16a34a 0%, #22c55e 50%, #4ade80 100%);
        }

        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-hover {
            transition: all 0.3s ease;
        }

        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px -12px rgba(0, 0, 0, 0.25);
        }

        .btn-hover {
            transition: all 0.3s ease;
        }

        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px -5px rgba(0, 0, 0, 0.25);
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f5f9;
        }

        ::-webkit-scrollbar-thumb {
            background: #16a34a;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #22c55e;
        }

        /* Loading states */
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .spinner {
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    </style>
</head>

<body class="font-sans antialiased bg-gray-50 min-h-screen flex flex-col">
    <!-- Navigation -->
    <nav class="fixed w-full z-50 bg-white/90 backdrop-blur-md border-b border-gray-200 shadow-sm">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-lg overflow-hidden">
                        <img src="<?php echo $base_url; ?>/assets/logo-school.png" alt="Masbate Colleges Logo" class="w-full h-full object-cover">
                    </div>
                    <div>
                        <a href="<?php echo $base_url; ?>/index.php" class="text-xl font-bold text-nsc-primary hover:text-nsc-secondary transition-colors">
                            Masbate Colleges
                        </a>
                        <p class="text-xs text-gray-600">Online Enrollment System</p>
                    </div>
                </div>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <?php if (isset($_SESSION['user_id'])): ?>
                        <?php if ($_SESSION['user_type'] === 'admin'): ?>
                            <a href="<?php echo $base_url; ?>/admin/dashboard.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                            </a>
                            <a href="<?php echo $base_url; ?>/admin/enrollments.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-clipboard-list mr-2"></i>Enrollments
                            </a>
                            <a href="<?php echo $base_url; ?>/admin/students.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-users mr-2"></i>Students
                            </a>
                            <a href="<?php echo $base_url; ?>/admin/reports.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-chart-bar mr-2"></i>Reports
                            </a>
                        <?php else: ?>
                            <a href="<?php echo $base_url; ?>/student/dashboard.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                            </a>
                            <a href="<?php echo $base_url; ?>/student/enroll.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-plus-circle mr-2"></i>Enroll
                            </a>
                            <a href="<?php echo $base_url; ?>/student/enrollments.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-list mr-2"></i>My Enrollments
                            </a>
                            <a href="<?php echo $base_url; ?>/student/profile.php" class="text-gray-700 hover:text-nsc-primary transition-colors flex items-center">
                                <i class="fas fa-user mr-2"></i>Profile
                            </a>
                        <?php endif; ?>

                        <!-- User Dropdown -->
                        <div class="relative group">
                            <button class="flex items-center space-x-2 text-gray-700 hover:text-nsc-primary transition-colors">
                                <i class="fas fa-user-circle"></i>
                                <span><?php echo $_SESSION['first_name']; ?></span>
                                <i class="fas fa-chevron-down text-xs"></i>
                            </button>
                            <div class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200">
                                <a href="<?php echo $base_url; ?>/logout.php" class="block px-4 py-3 text-gray-700 hover:bg-gray-50 rounded-lg flex items-center">
                                    <i class="fas fa-sign-out-alt mr-3"></i>Logout
                                </a>
                            </div>
                        </div>
                    <?php else: ?>
                        <a href="<?php echo $base_url; ?>/login.php" class="text-gray-700 hover:text-nsc-primary transition-colors">Login</a>
                        <a href="<?php echo $base_url; ?>/register.php" class="bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-primary/90 transition-colors">
                            Register
                        </a>
                    <?php endif; ?>
                </div>

                <!-- Mobile menu button -->
                <div class="md:hidden">
                    <button id="mobile-menu-btn" class="text-gray-700 hover:text-nsc-primary">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Mobile menu -->
        <div id="mobile-menu" class="hidden md:hidden bg-white border-t border-gray-200">
            <div class="px-4 py-3 space-y-3">
                <?php if (isset($_SESSION['user_id'])): ?>
                    <?php if ($_SESSION['user_type'] === 'admin'): ?>
                        <a href="<?php echo $base_url; ?>/admin/dashboard.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                        </a>
                        <a href="<?php echo $base_url; ?>/admin/enrollments.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-clipboard-list mr-2"></i>Enrollments
                        </a>
                        <a href="<?php echo $base_url; ?>/admin/students.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-users mr-2"></i>Students
                        </a>
                        <a href="<?php echo $base_url; ?>/admin/reports.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-chart-bar mr-2"></i>Reports
                        </a>
                    <?php else: ?>
                        <a href="<?php echo $base_url; ?>/student/dashboard.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-tachometer-alt mr-2"></i>Dashboard
                        </a>
                        <a href="<?php echo $base_url; ?>/student/enroll.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-plus-circle mr-2"></i>Enroll
                        </a>
                        <a href="<?php echo $base_url; ?>/student/enrollments.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-list mr-2"></i>My Enrollments
                        </a>
                        <a href="<?php echo $base_url; ?>/student/profile.php" class="block text-gray-700 hover:text-nsc-primary py-2">
                            <i class="fas fa-user mr-2"></i>Profile
                        </a>
                    <?php endif; ?>
                    <hr class="my-2">
                    <a href="<?php echo $base_url; ?>/logout.php" class="block text-red-600 hover:text-red-700 py-2">
                        <i class="fas fa-sign-out-alt mr-2"></i>Logout
                    </a>
                <?php else: ?>
                    <a href="<?php echo $base_url; ?>/login.php" class="block text-gray-700 hover:text-nsc-primary py-2">Login</a>
                    <a href="<?php echo $base_url; ?>/register.php" class="block bg-nsc-primary text-white px-4 py-2 rounded-lg text-center">Register</a>
                <?php endif; ?>
            </div>
        </div>
    </nav>

    <!-- Add top padding to account for fixed navbar -->
    <div class="pt-16 flex-1">

    <!-- Mobile Menu Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');

            if (mobileMenuBtn && mobileMenu) {
                mobileMenuBtn.addEventListener('click', function() {
                    mobileMenu.classList.toggle('hidden');
                });
            }
        });
    </script>
