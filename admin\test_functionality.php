<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// For testing purposes, we'll simulate admin login
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'admin';
    $_SESSION['first_name'] = 'Test';
    $_SESSION['last_name'] = 'Admin';
}

$db = new Database();
$conn = $db->getConnection();

$message = '';
$message_type = '';

// Test database connectivity and table existence
$tests = [];

// Test 1: Database connection
try {
    $conn->query("SELECT 1");
    $tests['database_connection'] = ['status' => 'success', 'message' => 'Database connection successful'];
} catch (Exception $e) {
    $tests['database_connection'] = ['status' => 'error', 'message' => 'Database connection failed: ' . $e->getMessage()];
}

// Test 2: Check required tables
$required_tables = ['users', 'courses', 'subjects', 'enrollments', 'enrollment_subjects', 'student_grades', 'payments'];
foreach ($required_tables as $table) {
    try {
        $result = $conn->query("SELECT COUNT(*) as count FROM $table");
        $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
        $tests["table_$table"] = ['status' => 'success', 'message' => "Table '$table' exists with $count records"];
    } catch (Exception $e) {
        $tests["table_$table"] = ['status' => 'error', 'message' => "Table '$table' missing or inaccessible"];
    }
}

// Test 3: Check required columns in enrollments table
$required_columns = ['verification_status', 'verification_notes', 'verified_at', 'verified_by'];
foreach ($required_columns as $column) {
    try {
        $result = $conn->query("SHOW COLUMNS FROM enrollments LIKE '$column'");
        if ($result->rowCount() > 0) {
            $tests["column_$column"] = ['status' => 'success', 'message' => "Column '$column' exists in enrollments table"];
        } else {
            $tests["column_$column"] = ['status' => 'error', 'message' => "Column '$column' missing from enrollments table"];
        }
    } catch (Exception $e) {
        $tests["column_$column"] = ['status' => 'error', 'message' => "Error checking column '$column': " . $e->getMessage()];
    }
}

// Test 4: Test sample data insertion (for student_grades)
try {
    // Check if we have users and subjects to work with
    $user_count = $conn->query("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'")->fetch()['count'];
    $subject_count = $conn->query("SELECT COUNT(*) as count FROM subjects")->fetch()['count'];
    
    if ($user_count > 0 && $subject_count > 0) {
        $tests['sample_data'] = ['status' => 'success', 'message' => "Sample data available: $user_count students, $subject_count subjects"];
    } else {
        $tests['sample_data'] = ['status' => 'warning', 'message' => "Limited sample data: $user_count students, $subject_count subjects"];
    }
} catch (Exception $e) {
    $tests['sample_data'] = ['status' => 'error', 'message' => 'Error checking sample data: ' . $e->getMessage()];
}

// Test 5: Test file accessibility
$required_files = [
    'get_grade.php' => 'Grade data retrieval',
    'records.php' => 'Records management',
    'registrar.php' => 'Registrar functions',
    'accounting.php' => 'Accounting functions'
];

foreach ($required_files as $file => $description) {
    if (file_exists($file)) {
        $tests["file_$file"] = ['status' => 'success', 'message' => "$description file exists"];
    } else {
        $tests["file_$file"] = ['status' => 'error', 'message' => "$description file missing"];
    }
}

$page_title = "Functionality Test";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#16a34a',
                        'nsc-secondary': '#22c55e'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <div class="max-w-6xl mx-auto p-6">
            <!-- Header -->
            <div class="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl shadow-xl text-white p-8 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">System Functionality Test</h1>
                        <p class="text-blue-100 text-lg">Testing database connectivity and functionality</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-vial text-4xl text-white opacity-80"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Results -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Test Results</h2>
                
                <div class="space-y-3">
                    <?php foreach ($tests as $test_name => $test_result): ?>
                        <div class="flex items-center justify-between p-3 rounded-lg border <?php 
                            echo $test_result['status'] === 'success' ? 'border-green-200 bg-green-50' : 
                                ($test_result['status'] === 'warning' ? 'border-yellow-200 bg-yellow-50' : 'border-red-200 bg-red-50'); 
                        ?>">
                            <div class="flex items-center">
                                <i class="fas <?php 
                                    echo $test_result['status'] === 'success' ? 'fa-check-circle text-green-600' : 
                                        ($test_result['status'] === 'warning' ? 'fa-exclamation-triangle text-yellow-600' : 'fa-times-circle text-red-600'); 
                                ?> mr-3"></i>
                                <div>
                                    <h3 class="font-semibold text-gray-800"><?php echo ucfirst(str_replace('_', ' ', $test_name)); ?></h3>
                                    <p class="text-sm text-gray-600"><?php echo htmlspecialchars($test_result['message']); ?></p>
                                </div>
                            </div>
                            <span class="px-3 py-1 rounded-full text-sm font-medium <?php 
                                echo $test_result['status'] === 'success' ? 'bg-green-100 text-green-800' : 
                                    ($test_result['status'] === 'warning' ? 'bg-yellow-100 text-yellow-800' : 'bg-red-100 text-red-800'); 
                            ?>">
                                <?php echo ucfirst($test_result['status']); ?>
                            </span>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
                <div class="flex flex-wrap gap-4">
                    <a href="init_database.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-database mr-2"></i>Initialize Database
                    </a>
                    <a href="records.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-folder-open mr-2"></i>Test Records
                    </a>
                    <a href="registrar.php" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-stamp mr-2"></i>Test Registrar
                    </a>
                    <a href="accounting.php" class="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors">
                        <i class="fas fa-calculator mr-2"></i>Test Accounting
                    </a>
                    <a href="dashboard.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
