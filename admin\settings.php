<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Handle session messages
if (isset($_SESSION['message'])) {
    $success_message = $_SESSION['message'];
    unset($_SESSION['message']);
}
if (isset($_SESSION['message_type'])) {
    unset($_SESSION['message_type']);
}

// Get current settings (we'll create a settings table structure)
$default_settings = [
    'school_name' => 'Masbate Colleges',
    'school_address' => 'Masbate City, Masbate',
    'school_phone' => '(*************',
    'school_email' => '<EMAIL>',
    'current_semester' => '1',
    'current_school_year' => '2024-2025',
    'enrollment_fee' => '5000.00',
    'late_enrollment_fee' => '500.00',
    'system_maintenance' => '0',
    // Payment Settings
    'gcash_number' => '09XX-XXX-XXXX',
    'gcash_name' => 'Masbate Colleges',
    'bank_name' => 'BPI',
    'bank_account_number' => 'XXXXXXXXXX',
    'bank_account_name' => 'Masbate Colleges',
    'otc_location' => 'Registrar\'s Office',
    'otc_hours' => 'Monday-Friday, 8:00 AM - 5:00 PM',
    'payment_instructions' => 'Please ensure payment amount matches the total fees exactly.'
];

// Create settings table if it doesn't exist
try {
    $conn->exec("CREATE TABLE IF NOT EXISTS system_settings (
        id INT AUTO_INCREMENT PRIMARY KEY,
        setting_key VARCHAR(100) UNIQUE NOT NULL,
        setting_value TEXT,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        updated_by INT,
        FOREIGN KEY (updated_by) REFERENCES users(id)
    )");
    
    // Insert default settings if table is empty
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM system_settings");
    $stmt->execute();
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count == 0) {
        foreach ($default_settings as $key => $value) {
            $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)");
            $stmt->execute([$key, $value, $_SESSION['user_id']]);
        }
    }
} catch (Exception $e) {
    // Table creation failed, use defaults
}

// Get current settings from database
$settings = $default_settings;
try {
    $stmt = $conn->prepare("SELECT setting_key, setting_value FROM system_settings");
    $stmt->execute();
    $db_settings = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    foreach ($db_settings as $setting) {
        $settings[$setting['setting_key']] = $setting['setting_value'];
    }
} catch (Exception $e) {
    // Use defaults if query fails
}

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];

        // Debug: Log the action being performed
        error_log("Settings action: " . $action);
        
        if ($action === 'update_school_info') {
            $school_name = sanitizeInput($_POST['school_name']);
            $school_address = sanitizeInput($_POST['school_address']);
            $school_phone = sanitizeInput($_POST['school_phone']);
            $school_email = sanitizeInput($_POST['school_email']);
            
            if (empty($school_name) || empty($school_address)) {
                $error_message = 'Please fill in all required fields.';
            } else {
                try {
                    $updates = [
                        'school_name' => $school_name,
                        'school_address' => $school_address,
                        'school_phone' => $school_phone,
                        'school_email' => $school_email
                    ];
                    
                    foreach ($updates as $key => $value) {
                        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)
                                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?");
                        if (!$stmt->execute([$key, $value, $_SESSION['user_id'], $value, $_SESSION['user_id']])) {
                            throw new Exception("Failed to update setting: " . $key);
                        }
                        $settings[$key] = $value;
                    }

                    $_SESSION['message'] = 'School information updated successfully!';
                    $_SESSION['message_type'] = 'success';
                    logActivity($_SESSION['user_id'], 'settings_updated', 'School information updated');

                    // Redirect to prevent form resubmission
                    header('Location: settings.php');
                    exit();
                } catch (Exception $e) {
                    $error_message = 'Failed to update school information: ' . $e->getMessage();
                    error_log("School info update error: " . $e->getMessage());
                }
            }
        } elseif ($action === 'update_academic_settings') {
            $current_semester = $_POST['current_semester'];
            $current_school_year = sanitizeInput($_POST['current_school_year']);
            $enrollment_fee = $_POST['enrollment_fee'];
            $late_enrollment_fee = $_POST['late_enrollment_fee'];
            
            if (empty($current_school_year) || $enrollment_fee < 0 || $late_enrollment_fee < 0) {
                $error_message = 'Please provide valid academic settings.';
            } else {
                try {
                    $updates = [
                        'current_semester' => $current_semester,
                        'current_school_year' => $current_school_year,
                        'enrollment_fee' => $enrollment_fee,
                        'late_enrollment_fee' => $late_enrollment_fee
                    ];
                    
                    foreach ($updates as $key => $value) {
                        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)
                                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?");
                        if (!$stmt->execute([$key, $value, $_SESSION['user_id'], $value, $_SESSION['user_id']])) {
                            throw new Exception("Failed to update setting: " . $key);
                        }
                        $settings[$key] = $value;
                    }

                    $_SESSION['message'] = 'Academic settings updated successfully!';
                    $_SESSION['message_type'] = 'success';
                    logActivity($_SESSION['user_id'], 'settings_updated', 'Academic settings updated');

                    // Redirect to prevent form resubmission
                    header('Location: settings.php');
                    exit();
                } catch (Exception $e) {
                    $error_message = 'Failed to update academic settings: ' . $e->getMessage();
                    error_log("Academic settings update error: " . $e->getMessage());
                }
            }
        } elseif ($action === 'update_payment_settings') {
            // Debug: Log payment settings update
            error_log("Payment settings update started");

            $gcash_number = sanitizeInput($_POST['gcash_number']);
            $gcash_name = sanitizeInput($_POST['gcash_name']);
            $bank_name = sanitizeInput($_POST['bank_name']);
            $bank_account_number = sanitizeInput($_POST['bank_account_number']);
            $bank_account_name = sanitizeInput($_POST['bank_account_name']);
            $otc_location = sanitizeInput($_POST['otc_location']);
            $otc_hours = sanitizeInput($_POST['otc_hours']);
            $payment_instructions = sanitizeInput($_POST['payment_instructions']);

            // Debug: Log received data
            error_log("Received payment data: GCash: $gcash_number, Bank: $bank_name");

            if (empty($gcash_number) || empty($gcash_name) || empty($bank_name) || empty($bank_account_number)) {
                $error_message = 'Please fill in all required payment fields.';
                error_log("Payment settings validation failed: missing required fields");
            } else {
                try {
                    $updates = [
                        'gcash_number' => $gcash_number,
                        'gcash_name' => $gcash_name,
                        'bank_name' => $bank_name,
                        'bank_account_number' => $bank_account_number,
                        'bank_account_name' => $bank_account_name,
                        'otc_location' => $otc_location,
                        'otc_hours' => $otc_hours,
                        'payment_instructions' => $payment_instructions
                    ];

                    foreach ($updates as $key => $value) {
                        $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)
                                              ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?");
                        if (!$stmt->execute([$key, $value, $_SESSION['user_id'], $value, $_SESSION['user_id']])) {
                            throw new Exception("Failed to update setting: " . $key);
                        }
                        $settings[$key] = $value;
                    }

                    $_SESSION['message'] = 'Payment settings updated successfully!';
                    $_SESSION['message_type'] = 'success';
                    logActivity($_SESSION['user_id'], 'settings_updated', 'Payment settings updated');

                    // Redirect to prevent form resubmission
                    header('Location: settings.php');
                    exit();
                } catch (Exception $e) {
                    $error_message = 'Failed to update payment settings: ' . $e->getMessage();
                    error_log("Payment settings update error: " . $e->getMessage());
                }
            }
        } elseif ($action === 'update_system_settings') {
            $system_maintenance = isset($_POST['system_maintenance']) ? '1' : '0';

            try {
                $stmt = $conn->prepare("INSERT INTO system_settings (setting_key, setting_value, updated_by) VALUES (?, ?, ?)
                                      ON DUPLICATE KEY UPDATE setting_value = ?, updated_by = ?");
                if (!$stmt->execute(['system_maintenance', $system_maintenance, $_SESSION['user_id'], $system_maintenance, $_SESSION['user_id']])) {
                    throw new Exception("Failed to update system maintenance setting");
                }
                $settings['system_maintenance'] = $system_maintenance;

                $_SESSION['message'] = 'System settings updated successfully!';
                $_SESSION['message_type'] = 'success';
                logActivity($_SESSION['user_id'], 'settings_updated', 'System settings updated');

                // Redirect to prevent form resubmission
                header('Location: settings.php');
                exit();
            } catch (Exception $e) {
                $error_message = 'Failed to update system settings: ' . $e->getMessage();
                error_log("System settings update error: " . $e->getMessage());
            }
        }
    }
}

$page_title = "System Settings";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-cogs text-2xl text-nsc-primary"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">System Settings</h1>
                            <p class="text-gray-600">Configure system-wide settings and preferences</p>
                        </div>
                    </div>
                </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- School Information -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-university text-blue-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">School Information</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="POST" action="" id="schoolForm">
                                <input type="hidden" name="action" value="update_school_info">
                                
                                <div class="space-y-4">
                                    <div>
                                        <label for="school_name" class="block text-sm font-medium text-gray-700 mb-2">School Name *</label>
                                        <input type="text" id="school_name" name="school_name" required
                                               value="<?php echo htmlspecialchars($settings['school_name']); ?>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                    
                                    <div>
                                        <label for="school_address" class="block text-sm font-medium text-gray-700 mb-2">School Address *</label>
                                        <textarea id="school_address" name="school_address" required rows="3"
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors"><?php echo htmlspecialchars($settings['school_address']); ?></textarea>
                                    </div>
                                    
                                    <div>
                                        <label for="school_phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input type="tel" id="school_phone" name="school_phone"
                                               value="<?php echo htmlspecialchars($settings['school_phone']); ?>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                    
                                    <div>
                                        <label for="school_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                                        <input type="email" id="school_email" name="school_email"
                                               value="<?php echo htmlspecialchars($settings['school_email']); ?>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                </div>
                                
                                <div class="flex justify-end mt-6">
                                    <button type="submit" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Update School Info
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Academic Settings -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-calendar-alt text-green-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">Academic Settings</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="POST" action="" id="academicForm">
                                <input type="hidden" name="action" value="update_academic_settings">
                                
                                <div class="space-y-4">
                                    <div>
                                        <label for="current_semester" class="block text-sm font-medium text-gray-700 mb-2">Current Semester</label>
                                        <select id="current_semester" name="current_semester" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            <option value="1" <?php echo $settings['current_semester'] == '1' ? 'selected' : ''; ?>>1st Semester</option>
                                            <option value="2" <?php echo $settings['current_semester'] == '2' ? 'selected' : ''; ?>>2nd Semester</option>
                                            <option value="3" <?php echo $settings['current_semester'] == '3' ? 'selected' : ''; ?>>Summer</option>
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="current_school_year" class="block text-sm font-medium text-gray-700 mb-2">Current School Year</label>
                                        <input type="text" id="current_school_year" name="current_school_year" required
                                               value="<?php echo htmlspecialchars($settings['current_school_year']); ?>"
                                               placeholder="e.g., 2024-2025"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                    
                                    <div>
                                        <label for="enrollment_fee" class="block text-sm font-medium text-gray-700 mb-2">Enrollment Fee (₱)</label>
                                        <input type="number" id="enrollment_fee" name="enrollment_fee" min="0" step="0.01"
                                               value="<?php echo htmlspecialchars($settings['enrollment_fee']); ?>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                    
                                    <div>
                                        <label for="late_enrollment_fee" class="block text-sm font-medium text-gray-700 mb-2">Late Enrollment Fee (₱)</label>
                                        <input type="number" id="late_enrollment_fee" name="late_enrollment_fee" min="0" step="0.01"
                                               value="<?php echo htmlspecialchars($settings['late_enrollment_fee']); ?>"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                    </div>
                                </div>
                                
                                <div class="flex justify-end mt-6">
                                    <button type="submit" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Update Academic Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Payment Settings -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-credit-card text-purple-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">Payment Settings</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="POST" action="" id="paymentForm">
                                <input type="hidden" name="action" value="update_payment_settings">

                                <div class="space-y-6">
                                    <!-- GCash Settings -->
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <h3 class="text-lg font-medium text-blue-900 mb-4 flex items-center">
                                            <i class="fas fa-mobile-alt mr-2"></i>GCash Settings
                                        </h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label for="gcash_number" class="block text-sm font-medium text-gray-700 mb-2">GCash Number *</label>
                                                <input type="text" id="gcash_number" name="gcash_number" required
                                                       value="<?php echo htmlspecialchars($settings['gcash_number']); ?>"
                                                       placeholder="09XX-XXX-XXXX"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                            <div>
                                                <label for="gcash_name" class="block text-sm font-medium text-gray-700 mb-2">Account Name *</label>
                                                <input type="text" id="gcash_name" name="gcash_name" required
                                                       value="<?php echo htmlspecialchars($settings['gcash_name']); ?>"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Bank Transfer Settings -->
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h3 class="text-lg font-medium text-green-900 mb-4 flex items-center">
                                            <i class="fas fa-university mr-2"></i>Bank Transfer Settings
                                        </h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label for="bank_name" class="block text-sm font-medium text-gray-700 mb-2">Bank Name *</label>
                                                <input type="text" id="bank_name" name="bank_name" required
                                                       value="<?php echo htmlspecialchars($settings['bank_name']); ?>"
                                                       placeholder="e.g., BPI, BDO, Metrobank"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                            <div>
                                                <label for="bank_account_number" class="block text-sm font-medium text-gray-700 mb-2">Account Number *</label>
                                                <input type="text" id="bank_account_number" name="bank_account_number" required
                                                       value="<?php echo htmlspecialchars($settings['bank_account_number']); ?>"
                                                       placeholder="XXXXXXXXXX"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                            <div class="md:col-span-2">
                                                <label for="bank_account_name" class="block text-sm font-medium text-gray-700 mb-2">Account Name *</label>
                                                <input type="text" id="bank_account_name" name="bank_account_name" required
                                                       value="<?php echo htmlspecialchars($settings['bank_account_name']); ?>"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Over-the-Counter Settings -->
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <h3 class="text-lg font-medium text-yellow-900 mb-4 flex items-center">
                                            <i class="fas fa-store mr-2"></i>Over-the-Counter Settings
                                        </h3>
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <label for="otc_location" class="block text-sm font-medium text-gray-700 mb-2">Payment Location</label>
                                                <input type="text" id="otc_location" name="otc_location"
                                                       value="<?php echo htmlspecialchars($settings['otc_location']); ?>"
                                                       placeholder="e.g., Registrar's Office"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                            <div>
                                                <label for="otc_hours" class="block text-sm font-medium text-gray-700 mb-2">Office Hours</label>
                                                <input type="text" id="otc_hours" name="otc_hours"
                                                       value="<?php echo htmlspecialchars($settings['otc_hours']); ?>"
                                                       placeholder="e.g., Monday-Friday, 8:00 AM - 5:00 PM"
                                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors">
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Payment Instructions -->
                                    <div>
                                        <label for="payment_instructions" class="block text-sm font-medium text-gray-700 mb-2">Payment Instructions</label>
                                        <textarea id="payment_instructions" name="payment_instructions" rows="3"
                                                  placeholder="Additional instructions for students..."
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-transparent transition-colors"><?php echo htmlspecialchars($settings['payment_instructions']); ?></textarea>
                                    </div>
                                </div>

                                <div class="flex justify-end mt-6">
                                    <button type="submit" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Update Payment Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- System Settings -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-server text-yellow-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">System Settings</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <form method="POST" action="" id="systemForm">
                                <input type="hidden" name="action" value="update_system_settings">
                                
                                <div class="space-y-6">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <h3 class="text-lg font-medium text-gray-900">Maintenance Mode</h3>
                                            <p class="text-sm text-gray-600">Enable to prevent student access during system updates</p>
                                        </div>
                                        <label class="relative inline-flex items-center cursor-pointer">
                                            <input type="checkbox" name="system_maintenance" class="sr-only peer" <?php echo $settings['system_maintenance'] == '1' ? 'checked' : ''; ?>>
                                            <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="flex justify-end mt-6">
                                    <button type="submit" class="bg-yellow-600 text-white px-6 py-3 rounded-lg hover:bg-yellow-700 transition-colors font-medium">
                                        <i class="fas fa-save mr-2"></i>Update System Settings
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- System Information -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-info-circle text-purple-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-800">System Information</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">System Version:</span>
                                    <span class="text-gray-900 font-medium">v1.0.0</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">PHP Version:</span>
                                    <span class="text-gray-900 font-medium"><?php echo phpversion(); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Database:</span>
                                    <span class="text-gray-900 font-medium">MySQL</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-gray-600">Last Updated:</span>
                                    <span class="text-gray-900 font-medium"><?php echo date('M d, Y g:i A'); ?></span>
                                </div>
                            </div>
                            
                            <div class="mt-6 pt-6 border-t border-gray-200">
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-start">
                                        <i class="fas fa-lightbulb text-blue-600 mr-3 mt-1"></i>
                                        <div>
                                            <h4 class="text-blue-900 font-medium mb-1">System Tips</h4>
                                            <p class="text-blue-800 text-sm">Regular backups and updates ensure optimal system performance and security.</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

<!-- JavaScript removed to prevent conflicts -->

<?php include '../includes/admin_layout_end.php'; ?>
