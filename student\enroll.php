<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get current semester and school year
$current_period = getCurrentSemesterYear();

// Check if student already has enrollment for current period
$stmt = $conn->prepare("SELECT * FROM enrollments 
                       WHERE student_id = ? AND semester = ? AND school_year = ?");
$stmt->execute([$_SESSION['user_id'], $current_period['semester'], $current_period['school_year']]);
$existing_enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if ($existing_enrollment) {
    header('Location: view-enrollment.php?id=' . $existing_enrollment['id']);
    exit();
}

// Get student info
$stmt = $conn->prepare("SELECT u.*, c.course_name, c.course_code FROM users u 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE u.id = ?");
$stmt->execute([$_SESSION['user_id']]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Get available subjects for student's course and year
$stmt = $conn->prepare("SELECT * FROM subjects 
                       WHERE course_id = ? AND year_level = ? AND semester = ? AND status = 'active' 
                       ORDER BY subject_code");
$stmt->execute([$student['course_id'], $student['year_level'], $current_period['semester']]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

$error_message = '';
$success_message = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $selected_subjects = $_POST['subjects'] ?? [];
    
    if (empty($selected_subjects)) {
        $error_message = 'Please select at least one subject.';
    } else {
        // Calculate total units and fees
        $total_units = 0;
        $subject_ids = [];
        
        foreach ($selected_subjects as $subject_id) {
            $stmt = $conn->prepare("SELECT units FROM subjects WHERE id = ?");
            $stmt->execute([$subject_id]);
            $subject = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($subject) {
                $total_units += $subject['units'];
                $subject_ids[] = $subject_id;
            }
        }
        
        $total_fees = calculateFees($total_units);
        
        try {
            $conn->beginTransaction();
            
            // Create enrollment record
            $stmt = $conn->prepare("INSERT INTO enrollments (student_id, semester, school_year, total_units, total_fees, status) 
                                   VALUES (?, ?, ?, ?, ?, 'pending')");
            $stmt->execute([$_SESSION['user_id'], $current_period['semester'], $current_period['school_year'], $total_units, $total_fees]);
            $enrollment_id = $conn->lastInsertId();
            
            // Add selected subjects
            foreach ($subject_ids as $subject_id) {
                $stmt = $conn->prepare("INSERT INTO enrollment_subjects (enrollment_id, subject_id) VALUES (?, ?)");
                $stmt->execute([$enrollment_id, $subject_id]);
            }
            
            $conn->commit();
            
            // Log activity
            logActivity($_SESSION['user_id'], 'enrollment_created', "Created enrollment for {$current_period['semester']} semester {$current_period['school_year']}");
            
            header('Location: upload-payment.php?id=' . $enrollment_id);
            exit();
            
        } catch (Exception $e) {
            $conn->rollBack();
            $error_message = 'Failed to create enrollment. Please try again.';
        }
    }
}

$page_title = "New Enrollment";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-plus-circle mr-3 text-green-200 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-plus-circle text-2xl text-green-600"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">New Enrollment</h1>
                                <p class="text-gray-600">Select your subjects for <?php echo getSemesterName($current_period['semester']); ?> - <?php echo $current_period['school_year']; ?></p>
                            </div>
                        </div>
                        <a href="dashboard.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                        </a>
                    </div>
                </div>
            
                <!-- Enrollment Info -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Enrollment Information</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Student Name</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Student ID</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($student['student_id']); ?></p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Course</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo htmlspecialchars($student['course_code'] . ' - ' . $student['course_name']); ?></p>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Year Level</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo $student['year_level']; ?><?php echo getOrdinalSuffix($student['year_level']); ?> Year</p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">Semester</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo getSemesterName($current_period['semester']); ?></p>
                                </div>
                                <div>
                                    <span class="text-sm font-medium text-gray-500">School Year</span>
                                    <p class="text-lg font-semibold text-gray-900"><?php echo $current_period['school_year']; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if ($error_message): ?>
                    <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-3"></i>
                            <span><?php echo $error_message; ?></span>
                        </div>
                    </div>
                <?php endif; ?>
            
                <?php if (empty($subjects)): ?>
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-exclamation-triangle text-2xl text-yellow-600"></i>
                            </div>
                            <h3 class="text-xl font-bold text-gray-900 mb-2">No Subjects Available</h3>
                            <p class="text-gray-600 mb-2">There are no subjects available for your course and year level in the current semester.</p>
                            <p class="text-gray-600 mb-6">Please contact the registrar's office for assistance.</p>
                            <a href="dashboard.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                Back to Dashboard
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                <form method="POST" action="" id="enrollmentForm">
                    <!-- Subject Selection -->
                    <div class="bg-white rounded-2xl shadow-lg mb-8">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-book text-green-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-900">Select Subjects</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="overflow-x-auto">
                                <table class="w-full">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700 w-16">Select</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700 w-20">Units</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Prerequisite</th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-100">
                                        <?php foreach ($subjects as $subject): ?>
                                            <tr class="hover:bg-gray-50 transition-colors">
                                                <td class="py-4 px-4">
                                                    <div class="flex items-center">
                                                        <input class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2 subject-checkbox"
                                                               type="checkbox"
                                                               name="subjects[]"
                                                               value="<?php echo $subject['id']; ?>"
                                                               data-units="<?php echo $subject['units']; ?>"
                                                               id="subject_<?php echo $subject['id']; ?>">
                                                    </div>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <label for="subject_<?php echo $subject['id']; ?>" class="font-semibold text-gray-900 cursor-pointer">
                                                        <?php echo htmlspecialchars($subject['subject_code']); ?>
                                                    </label>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <label for="subject_<?php echo $subject['id']; ?>" class="text-gray-700 cursor-pointer">
                                                        <?php echo htmlspecialchars($subject['subject_name']); ?>
                                                    </label>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                        <?php echo $subject['units']; ?>
                                                    </span>
                                                </td>
                                                <td class="py-4 px-4">
                                                    <?php echo $subject['prerequisite'] ? htmlspecialchars($subject['prerequisite']) : '<span class="text-gray-500">None</span>'; ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <div class="mt-6 flex gap-3">
                                <button type="button" class="px-4 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors font-medium" id="selectAll">
                                    <i class="fas fa-check-square mr-2"></i>Select All
                                </button>
                                <button type="button" class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium" id="clearAll">
                                    <i class="fas fa-square mr-2"></i>Clear All
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Fee Summary -->
                    <div class="bg-white rounded-2xl shadow-lg mb-8">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-calculator text-yellow-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-900">Fee Summary</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <div class="space-y-4">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Total Units:</span>
                                        <span class="font-semibold text-gray-900" id="total-units">0</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Rate per Unit:</span>
                                        <span class="text-gray-900">₱500.00</span>
                                    </div>
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-600">Miscellaneous Fee:</span>
                                        <span class="text-gray-900">₱2,000.00</span>
                                    </div>
                                    <hr class="border-gray-200">
                                    <div class="flex justify-between items-center">
                                        <span class="font-bold text-gray-900">Total Fees:</span>
                                        <span class="font-bold text-green-600 text-xl" id="total-fees">₱2,000.00</span>
                                    </div>
                                </div>
                                <div>
                                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                        <div class="flex items-start">
                                            <i class="fas fa-info-circle text-blue-600 mr-3 mt-1"></i>
                                            <div>
                                                <h4 class="font-semibold text-blue-900 mb-2">Payment Instructions:</h4>
                                                <p class="text-blue-800 text-sm">
                                                    After selecting subjects, you'll be redirected to upload your proof of payment.
                                                    Accepted payment methods: GCash, Bank Transfer, Over-the-counter payment.
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-8 text-center">
                            <button type="submit" class="bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg btn-hover" id="submitBtn" disabled>
                                <i class="fas fa-arrow-right mr-2"></i>Proceed to Payment Upload
                            </button>
                            <p class="text-gray-500 mt-4 mb-0">Please select at least one subject to continue</p>
                        </div>
                    </div>
                </form>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
function getOrdinalSuffix(number) {
    const ends = ['th','st','nd','rd','th','th','th','th','th','th'];
    if ((number % 100) >= 11 && (number % 100) <= 13)
        return 'th';
    else
        return ends[number % 10];
}

// Calculate total fees function
function calculateTotalFees() {
    const checkboxes = document.querySelectorAll('.subject-checkbox:checked');
    let totalUnits = 0;

    checkboxes.forEach(checkbox => {
        totalUnits += parseInt(checkbox.dataset.units);
    });

    const totalFees = (totalUnits * 500) + 2000; // 500 per unit + 2000 misc fee

    document.getElementById('total-units').textContent = totalUnits;
    document.getElementById('total-fees').textContent = '₱' + totalFees.toLocaleString() + '.00';

    // Enable/disable submit button
    const submitBtn = document.getElementById('submitBtn');
    if (totalUnits > 0) {
        submitBtn.disabled = false;
        submitBtn.classList.remove('opacity-50', 'cursor-not-allowed');
        submitBtn.classList.add('btn-hover');
    } else {
        submitBtn.disabled = true;
        submitBtn.classList.add('opacity-50', 'cursor-not-allowed');
        submitBtn.classList.remove('btn-hover');
    }
}

// Add event listeners when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Add change event to all checkboxes
    const checkboxes = document.querySelectorAll('.subject-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', calculateTotalFees);
    });

    // Select All button
    document.getElementById('selectAll').addEventListener('click', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });
        calculateTotalFees();
    });

    // Clear All button
    document.getElementById('clearAll').addEventListener('click', function() {
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });
        calculateTotalFees();
    });

    // Initial calculation
    calculateTotalFees();
});
</script>

</body>
</html>
