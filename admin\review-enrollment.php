<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Generate CSRF token
if (!isset($_SESSION['csrf_token'])) {
    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
}

// Get enrollment ID
$enrollment_id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if ($enrollment_id <= 0) {
    header('Location: enrollments.php?error=invalid_enrollment_id');
    exit();
}

// Get enrollment details
$stmt = $conn->prepare("SELECT e.*, u.id as user_id, u.first_name, u.last_name, u.middle_name, u.student_id, u.email, u.contact_number, u.address, u.year_level,
                       c.course_code, c.course_name
                       FROM enrollments e
                       JOIN users u ON e.student_id = u.id
                       LEFT JOIN courses c ON u.course_id = c.id
                       WHERE e.id = ?");
$stmt->execute([$enrollment_id]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: enrollments.php?error=enrollment_not_found');
    exit();
}

// Get enrolled subjects
$stmt = $conn->prepare("SELECT s.* FROM subjects s
                       JOIN enrollment_subjects es ON s.id = es.subject_id
                       WHERE es.enrollment_id = ?
                       ORDER BY s.subject_code");
$stmt->execute([$enrollment_id]);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

$error_message = '';
$success_message = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])) {
        $error_message = 'Invalid security token. Please refresh the page and try again.';
    } elseif (!isset($_POST['action']) || empty($_POST['action'])) {
        $error_message = 'Invalid form submission. Please select an action.';
    } else {
        $action = $_POST['action'];
        $remarks = isset($_POST['remarks']) ? sanitizeInput($_POST['remarks']) : '';

        // Validate action
        if (!in_array($action, ['approve', 'return'])) {
            $error_message = 'Invalid action specified.';
        } elseif ($enrollment['status'] !== 'pending') {
            $error_message = 'This enrollment has already been processed.';
        } else {
            try {
                $conn->beginTransaction();

                if ($action === 'approve') {
                    $stmt = $conn->prepare("UPDATE enrollments SET status = 'approved', approved_at = NOW(), approved_by = ?, remarks = ? WHERE id = ? AND status = 'pending'");
                    if ($stmt->execute([$_SESSION['user_id'], $remarks, $enrollment_id])) {
                        if ($stmt->rowCount() > 0) {
                            // Send notification to student
                            sendNotification($enrollment['user_id'], 'Enrollment Approved', 'Your enrollment has been approved. You can now download your Certificate of Registration.');

                            // Log activity
                            logActivity($_SESSION['user_id'], 'enrollment_approved', "Approved enrollment ID: $enrollment_id for student: {$enrollment['student_id']}");

                            $success_message = 'Enrollment approved successfully!';
                            $enrollment['status'] = 'approved';
                            $enrollment['approved_at'] = date('Y-m-d H:i:s');
                            $enrollment['approved_by'] = $_SESSION['user_id'];
                            $enrollment['remarks'] = $remarks;
                        } else {
                            throw new Exception('Enrollment could not be updated. It may have already been processed.');
                        }
                    } else {
                        throw new Exception('Failed to approve enrollment.');
                    }
                } elseif ($action === 'return') {
                    if (empty($remarks)) {
                        $error_message = 'Please provide remarks for returning the enrollment.';
                    } else {
                        $stmt = $conn->prepare("UPDATE enrollments SET status = 'returned', remarks = ? WHERE id = ? AND status = 'pending'");
                        if ($stmt->execute([$remarks, $enrollment_id])) {
                            if ($stmt->rowCount() > 0) {
                                // Send notification to student
                                sendNotification($enrollment['user_id'], 'Enrollment Returned', "Your enrollment has been returned. Remarks: $remarks");

                                // Log activity
                                logActivity($_SESSION['user_id'], 'enrollment_returned', "Returned enrollment ID: $enrollment_id for student: {$enrollment['student_id']}");

                                $success_message = 'Enrollment returned successfully!';
                                $enrollment['status'] = 'returned';
                                $enrollment['remarks'] = $remarks;
                            } else {
                                throw new Exception('Enrollment could not be updated. It may have already been processed.');
                            }
                        } else {
                            throw new Exception('Failed to return enrollment.');
                        }
                    }
                }

                if (empty($error_message)) {
                    $conn->commit();
                    // Regenerate CSRF token after successful action
                    $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
                } else {
                    $conn->rollBack();
                }

            } catch (Exception $e) {
                $conn->rollBack();
                $error_message = $e->getMessage();
            }
        }
    }
}

$page_title = "Review Enrollment";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-edit text-2xl text-nsc-primary"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Review Enrollment</h1>
                                <p class="text-gray-600">Review and manage enrollment application</p>
                            </div>
                        </div>
                        <a href="enrollments.php" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Enrollments
                        </a>
                    </div>
                </div>

            <?php if ($error_message): ?>
                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle text-green-600 mr-2"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Student Information -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Student Information</h2>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Student ID:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['student_id']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Name:</span>
                            <span class="text-gray-900">
                                <?php echo htmlspecialchars($enrollment['first_name'] . ' ' .
                                    ($enrollment['middle_name'] ? $enrollment['middle_name'] . ' ' : '') .
                                    $enrollment['last_name']); ?>
                            </span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Email:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['email']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Contact:</span>
                            <span class="text-gray-900"><?php echo htmlspecialchars($enrollment['contact_number']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Course:</span>
                            <span class="text-gray-900">
                                <?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?>
                            </span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Year Level:</span>
                            <span class="text-gray-900"><?php echo $enrollment['year_level']; ?><?php echo getOrdinalSuffix($enrollment['year_level']); ?> Year</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Enrollment Details</h2>
                        </div>
                    </div>
                    <div class="p-6 space-y-4">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Period:</span>
                            <span class="text-gray-900">
                                <?php echo getSemesterName($enrollment['semester']); ?> -
                                <?php echo $enrollment['school_year']; ?>
                            </span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Total Units:</span>
                            <span class="text-gray-900"><?php echo $enrollment['total_units']; ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Total Fees:</span>
                            <span class="font-bold text-blue-600 text-lg"><?php echo formatCurrency($enrollment['total_fees']); ?></span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Status:</span>
                            <span>
                                <?php
                                $status_colors = [
                                    'approved' => 'bg-green-100 text-green-800',
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'returned' => 'bg-red-100 text-red-800'
                                ];
                                $status_class = $status_colors[$enrollment['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                    <?php echo ucfirst($enrollment['status']); ?>
                                </span>
                            </span>
                        </div>
                        <hr class="border-gray-200">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold text-gray-700">Submitted:</span>
                            <span class="text-gray-900"><?php echo date('M d, Y g:i A', strtotime($enrollment['submitted_at'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Selected Subjects -->
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-book text-purple-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Selected Subjects</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700 w-20">Units</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Prerequisites</th>
                                    </tr>
                                </thead>
                                <tbody class="divide-y divide-gray-100">
                                    <?php foreach ($subjects as $subject): ?>
                                        <tr class="hover:bg-gray-50 transition-colors">
                                            <td class="py-4 px-4 font-semibold text-gray-900"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                            <td class="py-4 px-4">
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    <?php echo $subject['units']; ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4 text-gray-600"><?php echo htmlspecialchars($subject['prerequisite'] ?: 'None'); ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                <tfoot class="border-t-2 border-gray-300">
                                    <tr class="bg-blue-50">
                                        <td colspan="2" class="py-3 px-4 font-bold text-gray-900">Total Units</td>
                                        <td class="py-3 px-4 font-bold text-blue-600 text-lg"><?php echo $enrollment['total_units']; ?></td>
                                        <td class="py-3 px-4"></td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Proof -->
            <?php if ($enrollment['payment_proof']): ?>
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-receipt text-yellow-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-900">Payment Proof</h2>
                            </div>
                            <div class="text-sm text-gray-500">
                                <i class="fas fa-clock mr-1"></i>
                                Uploaded: <?php echo date('M d, Y H:i', strtotime($enrollment['submitted_at'])); ?>
                            </div>
                        </div>
                    </div>
                    <div class="p-8">
                        <?php
                        $file_extension = strtolower(pathinfo($enrollment['payment_proof'], PATHINFO_EXTENSION));
                        $file_path = '../uploads/payments/' . $enrollment['payment_proof'];
                        $file_size = file_exists($file_path) ? filesize($file_path) : 0;
                        $file_size_formatted = $file_size > 0 ? number_format($file_size / 1024 / 1024, 2) . ' MB' : 'Unknown';
                        ?>

                        <!-- File Information -->
                        <div class="bg-gray-50 rounded-lg p-4 mb-6">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                                <div>
                                    <span class="font-medium text-gray-700">File Name:</span>
                                    <p class="text-gray-900 break-all"><?php echo htmlspecialchars($enrollment['payment_proof']); ?></p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">File Type:</span>
                                    <p class="text-gray-900 uppercase"><?php echo $file_extension; ?></p>
                                </div>
                                <div>
                                    <span class="font-medium text-gray-700">File Size:</span>
                                    <p class="text-gray-900"><?php echo $file_size_formatted; ?></p>
                                </div>
                            </div>
                        </div>

                        <!-- File Preview -->
                        <div class="text-center">
                            <?php if (in_array($file_extension, ['jpg', 'jpeg', 'png']) && file_exists($file_path)): ?>
                                <div class="mb-6">
                                    <div class="inline-block border-4 border-white rounded-lg shadow-lg">
                                        <img src="<?php echo $file_path; ?>" alt="Payment Proof" class="max-h-96 max-w-full rounded-lg">
                                    </div>
                                    <p class="text-sm text-gray-600 mt-2">Click "View Full Size" to see the complete image</p>
                                </div>
                            <?php elseif ($file_extension === 'pdf'): ?>
                                <div class="mb-6">
                                    <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-file-pdf text-4xl text-red-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">PDF Document</h3>
                                    <p class="text-gray-600">Click "View Full Size" to open the PDF document</p>
                                </div>
                            <?php else: ?>
                                <div class="mb-6">
                                    <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                        <i class="fas fa-file text-4xl text-gray-600"></i>
                                    </div>
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">File Not Found</h3>
                                    <p class="text-red-600">The uploaded file could not be found or is corrupted</p>
                                </div>
                            <?php endif; ?>

                            <!-- Action Buttons -->
                            <?php if (file_exists($file_path)): ?>
                                <div class="flex flex-col sm:flex-row justify-center gap-3">
                                    <a href="<?php echo $file_path; ?>" target="_blank" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                        <i class="fas fa-external-link-alt mr-2"></i>View Full Size
                                    </a>
                                    <a href="<?php echo $file_path; ?>" download class="border border-blue-300 text-blue-700 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors font-medium">
                                        <i class="fas fa-download mr-2"></i>Download File
                                    </a>
                                    <?php if (in_array($file_extension, ['jpg', 'jpeg', 'png'])): ?>
                                        <button onclick="openImageModal('<?php echo $file_path; ?>')" class="border border-green-300 text-green-700 px-6 py-3 rounded-lg hover:bg-green-50 transition-colors font-medium">
                                            <i class="fas fa-search-plus mr-2"></i>Zoom View
                                        </button>
                                    <?php endif; ?>
                                </div>
                            <?php else: ?>
                                <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    File not accessible. Please contact the student to re-upload the payment proof.
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            <?php else: ?>
            <div class="mb-8">
                <div class="bg-yellow-50 border border-yellow-200 text-yellow-800 px-6 py-4 rounded-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-3"></i>
                        <div>
                            <p class="font-medium">No Payment Proof Uploaded</p>
                            <p class="text-sm">The student has not yet uploaded their payment proof for this enrollment.</p>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>

            <!-- Action Form -->
            <?php if ($enrollment['status'] === 'pending'): ?>
            <div class="bg-white rounded-2xl shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-gavel text-orange-600"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-900">Review Action</h2>
                    </div>
                </div>
                <div class="p-6">
                    <form method="POST" action="" id="reviewForm">
                        <input type="hidden" name="csrf_token" value="<?php echo htmlspecialchars($_SESSION['csrf_token']); ?>">

                        <div class="mb-6">
                            <label for="remarks" class="block text-sm font-medium text-gray-700 mb-2">Remarks</label>
                            <textarea class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                      id="remarks" name="remarks" rows="3"
                                      placeholder="Enter remarks (optional for approval, required for return)"><?php echo isset($_POST['remarks']) ? htmlspecialchars($_POST['remarks']) : ''; ?></textarea>
                        </div>

                        <div class="flex space-x-4">
                            <button type="button" onclick="submitForm('approve')" class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg" id="approveBtn">
                                <i class="fas fa-check mr-2"></i>Approve Enrollment
                            </button>
                            <button type="button" onclick="submitForm('return')" class="bg-red-600 text-white px-8 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium text-lg" id="returnBtn">
                                <i class="fas fa-times mr-2"></i>Return Enrollment
                            </button>
                        </div>

                        <!-- Hidden input for action -->
                        <input type="hidden" name="action" id="actionInput" value="">
                    </form>
                </div>
            </div>
            <?php else: ?>
            <div class="bg-blue-50 border border-blue-200 text-blue-800 px-6 py-4 rounded-lg">
                <div class="flex items-start">
                    <i class="fas fa-info-circle text-blue-600 mr-3 mt-1"></i>
                    <div>
                        <p class="font-medium">
                            This enrollment has already been <?php echo $enrollment['status']; ?>.
                        </p>
                        <?php if ($enrollment['remarks']): ?>
                            <div class="mt-2 p-3 bg-white bg-opacity-50 rounded-lg">
                                <p class="font-semibold mb-1">Remarks:</p>
                                <p><?php echo htmlspecialchars($enrollment['remarks']); ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            </div>

<script>
function submitForm(action) {
    const remarks = document.getElementById('remarks').value.trim();
    const actionInput = document.getElementById('actionInput');
    const form = document.getElementById('reviewForm');

    // Validation
    if (action === 'return' && !remarks) {
        alert('Please provide remarks when returning an enrollment.');
        document.getElementById('remarks').focus();
        return false;
    }

    // Confirmation
    const confirmMessage = action === 'approve' ?
        'Are you sure you want to approve this enrollment?' :
        'Are you sure you want to return this enrollment?';

    if (!confirm(confirmMessage)) {
        return false;
    }

    // Set the action value
    actionInput.value = action;

    // Show loading state on the clicked button
    const button = action === 'approve' ? document.getElementById('approveBtn') : document.getElementById('returnBtn');
    const originalText = button.innerHTML;

    // Disable both buttons and show loading
    document.getElementById('approveBtn').disabled = true;
    document.getElementById('returnBtn').disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';

    // Submit the form
    form.submit();
}

// Image modal functionality
function openImageModal(imageSrc) {
    // Create modal HTML
    const modalHTML = `
        <div id="imageModal" class="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4">
            <div class="relative max-w-full max-h-full">
                <button onclick="closeImageModal()" class="absolute top-4 right-4 text-white bg-black bg-opacity-50 rounded-full w-10 h-10 flex items-center justify-center hover:bg-opacity-75 transition-colors z-10">
                    <i class="fas fa-times"></i>
                </button>
                <img src="${imageSrc}" alt="Payment Proof - Full Size" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl">
                <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-4 py-2 rounded-lg">
                    <p class="text-sm">Click outside image or press ESC to close</p>
                </div>
            </div>
        </div>
    `;

    // Add modal to body
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Add event listeners
    const modal = document.getElementById('imageModal');

    // Close on click outside image
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeImageModal();
        }
    });

    // Close on ESC key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeImageModal();
        }
    });

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
}

function closeImageModal() {
    const modal = document.getElementById('imageModal');
    if (modal) {
        modal.remove();
        document.body.style.overflow = 'auto';
    }
}
</script>



<?php include '../includes/admin_layout_end.php'; ?>
