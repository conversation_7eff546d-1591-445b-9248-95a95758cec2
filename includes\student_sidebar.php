<?php
// Get current page name for active state
$current_page = basename($_SERVER['PHP_SELF']);
?>

<!-- Modern Student Sidebar -->
<div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
    <div class="p-6">
        <!-- Student <PERSON> Header -->
        <div class="mb-8">
            <div class="flex items-center mb-4">
                <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-user-graduate text-white text-lg"></i>
                </div>
                <div>
                    <h3 class="text-white font-bold text-lg">Student Portal</h3>
                    <p class="text-green-200 text-sm">Enrollment System</p>
                </div>
            </div>
        </div>

        <!-- Navigation Menu -->
        <nav class="space-y-2">
            <a href="dashboard.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'dashboard.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-tachometer-alt mr-3 <?php echo $current_page == 'dashboard.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Dashboard</span>
            </a>
            <a href="enroll.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'enroll.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-plus-circle mr-3 <?php echo $current_page == 'enroll.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">New Enrollment</span>
            </a>
            <a href="enrollments.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'enrollments.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-list mr-3 <?php echo $current_page == 'enrollments.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">My Enrollments</span>
            </a>
            <a href="subjects.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'subjects.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-book mr-3 <?php echo $current_page == 'subjects.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Available Subjects</span>
            </a>
            <a href="profile.php" class="flex items-center px-4 py-3 <?php echo $current_page == 'profile.php' ? 'text-white bg-white bg-opacity-20' : 'text-green-200 hover:bg-white hover:bg-opacity-10 hover:text-white'; ?> rounded-lg transition-all duration-300 group">
                <i class="fas fa-user mr-3 <?php echo $current_page == 'profile.php' ? 'text-green-200' : ''; ?> group-hover:text-white transition-colors"></i>
                <span class="font-medium">Profile</span>
            </a>
        </nav>
    </div>
</div>
