# Payment Settings Management Guide

## 🎯 Overview
The Masbate Colleges Online Enrollment System now includes a comprehensive Payment Settings management feature that allows administrators to easily update payment information without modifying code files.

## 🔧 How to Access Payment Settings

### Step 1: Login as Administrator
1. Go to the admin login page
2. Login with your administrator credentials
3. Navigate to the admin dashboard

### Step 2: Access Settings
1. Click on **"Settings"** in the admin sidebar
2. Scroll down to the **"Payment Settings"** section
3. This section has a purple header with a credit card icon

## 💳 Payment Settings Features

### GCash Settings
- **GCash Number**: Enter your institution's GCash number (e.g., 09XX-XXX-XXXX)
- **Account Name**: Enter the account holder name (e.g., Masbate Colleges)

### Bank Transfer Settings
- **Bank Name**: Enter the bank name (e.g., BPI, BDO, Metrobank)
- **Account Number**: Enter the bank account number
- **Account Name**: Enter the account holder name

### Over-the-Counter Settings
- **Payment Location**: Where students can pay in person (e.g., Registrar's Office)
- **Office Hours**: When the office is open for payments (e.g., Monday-Friday, 8:00 AM - 5:00 PM)

### Payment Instructions
- **Custom Instructions**: Additional instructions for students about payment procedures

## 📝 How to Update Payment Settings

### Step 1: Edit the Information
1. Navigate to **Admin → Settings → Payment Settings**
2. Update any of the payment fields:
   - GCash number and name
   - Bank details
   - Over-the-counter information
   - Payment instructions

### Step 2: Save Changes
1. Click the **"Update Payment Settings"** button
2. You'll see a success message confirming the update
3. Changes are immediately applied system-wide

### Step 3: Verify Changes
1. Go to any student payment page to verify the changes
2. Check that the new information appears correctly
3. Test with a student account if needed

## 🎯 Where Payment Settings Appear

### Student Upload Payment Page
The payment settings automatically appear in:
- **Payment Instructions section** on the right side
- **Accepted Payment Methods** showing:
  - GCash number and account name
  - Bank name, account number, and account name
  - Over-the-counter location and hours
- **Custom payment instructions** at the bottom

### Other Student Pages
- Payment option pages
- Enrollment confirmation pages
- Any page that displays payment information

## ✅ Benefits of Dynamic Payment Settings

### For Administrators
- **Easy Updates**: No need to edit code files
- **Instant Changes**: Updates apply immediately
- **Centralized Management**: All payment info in one place
- **Audit Trail**: Changes are logged with timestamps

### For Students
- **Always Current**: Payment information is always up-to-date
- **Clear Instructions**: Consistent payment guidance
- **Multiple Options**: All payment methods clearly displayed
- **Professional Appearance**: Clean, organized payment information

## 🔒 Security Features

### Access Control
- Only administrators can modify payment settings
- Changes are logged with user ID and timestamp
- Session-based authentication required

### Data Validation
- Required fields are enforced
- Input sanitization prevents malicious data
- Database transactions ensure data integrity

## 📋 Recommended Payment Information

### GCash
- Use the official institutional GCash number
- Ensure the account name matches official records
- Format: 09XX-XXX-XXXX for clarity

### Bank Transfer
- Use the primary institutional bank account
- Include full bank name (not abbreviations)
- Verify account number accuracy
- Use official account name

### Over-the-Counter
- Specify exact location (building, room, office)
- Include complete office hours
- Add contact information if needed

### Instructions
- Keep instructions clear and concise
- Include any special requirements
- Mention processing timeframes
- Add contact info for questions

## 🚀 Quick Setup Guide

### Initial Setup
1. **Login** as administrator
2. **Navigate** to Settings → Payment Settings
3. **Fill in** all required fields:
   - GCash number and name
   - Bank name, account number, and name
   - OTC location and hours
4. **Add** custom payment instructions
5. **Save** the settings
6. **Test** by viewing a student payment page

### Regular Updates
- Review payment information monthly
- Update when bank details change
- Modify office hours as needed
- Keep instructions current

## 📞 Support

If you need help with payment settings:
- Check this guide first
- Test changes on a student account
- Contact technical support if issues persist
- Keep backup of important payment information

## 🎯 Best Practices

### Accuracy
- Double-check all account numbers
- Verify bank names and details
- Test payment methods before publishing

### Clarity
- Use clear, simple language
- Provide complete information
- Include all necessary details

### Maintenance
- Review settings regularly
- Update when information changes
- Monitor for any issues

This payment settings feature makes managing your institution's payment information simple, secure, and efficient!
