<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$success_count = 0;
$error_count = 0;
$errors = [];

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['enrollment_ids'])) {
    $enrollment_ids = $_POST['enrollment_ids'];
    $remarks = isset($_POST['remarks']) ? sanitizeInput($_POST['remarks']) : 'Bulk approved';
    
    if (empty($enrollment_ids)) {
        header('Location: enrollments.php?error=no_selection');
        exit();
    }
    
    try {
        $conn->beginTransaction();
        
        foreach ($enrollment_ids as $enrollment_id) {
            $enrollment_id = (int)$enrollment_id;
            
            // Get enrollment details
            $stmt = $conn->prepare("SELECT e.*, u.id as user_id, u.student_id, u.first_name, u.last_name
                                   FROM enrollments e
                                   JOIN users u ON e.student_id = u.id
                                   WHERE e.id = ? AND e.status = 'pending'");
            $stmt->execute([$enrollment_id]);
            $enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($enrollment) {
                // Update enrollment status
                $stmt = $conn->prepare("UPDATE enrollments SET status = 'approved', approved_at = NOW(), approved_by = ?, remarks = ? WHERE id = ?");
                if ($stmt->execute([$_SESSION['user_id'], $remarks, $enrollment_id])) {
                    $success_count++;

                    // Send notification to student
                    sendNotification($enrollment['user_id'], 'Enrollment Approved', 'Your enrollment has been approved. You can now download your Certificate of Registration.');

                    // Log activity
                    logActivity($_SESSION['user_id'], 'enrollment_bulk_approved', "Bulk approved enrollment ID: $enrollment_id for student: {$enrollment['student_id']}");
                } else {
                    $error_count++;
                    $errors[] = "Failed to approve enrollment for student {$enrollment['student_id']}";
                }
            } else {
                $error_count++;
                $errors[] = "Enrollment ID $enrollment_id not found or already processed";
            }
        }
        
        $conn->commit();
        
        // Prepare success message
        $message = "Bulk approval completed: $success_count enrollment(s) approved";
        if ($error_count > 0) {
            $message .= ", $error_count failed";
        }
        
        // Redirect with results
        if ($error_count > 0) {
            $error_details = implode('; ', $errors);
            header("Location: enrollments.php?success=" . urlencode($message) . "&errors=" . urlencode($error_details));
        } else {
            header("Location: enrollments.php?success=" . urlencode($message));
        }
        exit();
        
    } catch (Exception $e) {
        $conn->rollBack();
        header('Location: enrollments.php?error=' . urlencode('Bulk approval failed: ' . $e->getMessage()));
        exit();
    }
} else {
    header('Location: enrollments.php?error=invalid_request');
    exit();
}
?>
