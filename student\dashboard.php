<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get current semester and school year
$current_period = getCurrentSemesterYear();

// Get student's course information
$stmt = $conn->prepare("SELECT u.*, c.course_name, c.course_code FROM users u 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE u.id = ?");
$stmt->execute([$_SESSION['user_id']]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Get student's enrollment history
$stmt = $conn->prepare("SELECT e.*, COUNT(es.subject_id) as subject_count 
                       FROM enrollments e 
                       LEFT JOIN enrollment_subjects es ON e.id = es.enrollment_id 
                       WHERE e.student_id = ? 
                       GROUP BY e.id 
                       ORDER BY e.submitted_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if student has pending enrollment for current period
$stmt = $conn->prepare("SELECT * FROM enrollments 
                       WHERE student_id = ? AND semester = ? AND school_year = ? AND status = 'pending'");
$stmt->execute([$_SESSION['user_id'], $current_period['semester'], $current_period['school_year']]);
$pending_enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

// Check if student has approved enrollment for current period
$stmt = $conn->prepare("SELECT * FROM enrollments 
                       WHERE student_id = ? AND semester = ? AND school_year = ? AND status = 'approved'");
$stmt->execute([$_SESSION['user_id'], $current_period['semester'], $current_period['school_year']]);
$approved_enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

$page_title = "Student Dashboard";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .status-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-tachometer-alt mr-3 text-green-200 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Welcome Section -->
            <div class="mb-8">
                <div class="bg-gradient-to-r from-green-600 to-green-700 rounded-2xl shadow-xl text-white p-8">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold mb-2">Welcome, <?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?>!</h1>
                            <div class="flex items-center text-green-100 text-lg mb-3">
                                <i class="fas fa-id-card mr-2"></i>
                                <span>Student ID: <strong><?php echo htmlspecialchars($student['student_id']); ?></strong></span>
                            </div>
                            <div class="flex items-center text-green-100">
                                <i class="fas fa-graduation-cap mr-2"></i>
                                <span>
                                    <?php echo htmlspecialchars($student['course_code']); ?> -
                                    <?php echo htmlspecialchars($student['course_name']); ?>
                                    (<?php echo $student['year_level']; ?><?php echo getOrdinalSuffix($student['year_level']); ?> Year)
                                </span>
                            </div>
                        </div>
                        <div class="hidden md:block">
                            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-graduate text-4xl text-white opacity-80"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Current Period Info -->
            <div class="mb-8">
                <div class="bg-blue-50 border border-blue-200 rounded-xl p-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-calendar-alt text-blue-600"></i>
                        </div>
                        <div>
                            <p class="text-blue-800 font-semibold">Current Enrollment Period</p>
                            <p class="text-blue-600">
                                <?php echo getSemesterName($current_period['semester']); ?> -
                                School Year <?php echo $current_period['school_year']; ?>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- New Enrollment Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-plus-circle text-2xl text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">New Enrollment</h3>
                        <p class="text-gray-600 text-sm mb-4">Start your enrollment for this semester</p>
                        <?php if ($approved_enrollment): ?>
                            <span class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                <i class="fas fa-check mr-2"></i>Already Enrolled
                            </span>
                        <?php elseif ($pending_enrollment): ?>
                            <span class="inline-flex items-center px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">
                                <i class="fas fa-clock mr-2"></i>Pending Approval
                            </span>
                        <?php else: ?>
                            <a href="enroll.php" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                Enroll Now
                            </a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- My Enrollments Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-list text-2xl text-blue-600"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">My Enrollments</h3>
                        <p class="text-gray-600 text-sm mb-4">View your enrollment history</p>
                        <a href="enrollments.php" class="border border-blue-600 text-blue-600 px-4 py-2 rounded-lg hover:bg-blue-600 hover:text-white transition-colors font-medium">
                            View All
                        </a>
                    </div>
                </div>

                <!-- Available Subjects Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="text-center">
                        <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-book text-2xl text-yellow-600"></i>
                        </div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">Available Subjects</h3>
                        <p class="text-gray-600 text-sm mb-4">Browse subjects for your course</p>
                        <a href="subjects.php" class="border border-yellow-600 text-yellow-600 px-4 py-2 rounded-lg hover:bg-yellow-600 hover:text-white transition-colors font-medium">
                            Browse
                        </a>
                    </div>
                </div>

                <!-- Download COR Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="text-center">
                        <?php if ($approved_enrollment): ?>
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-download text-2xl text-purple-600"></i>
                            </div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2">Download COR</h3>
                            <p class="text-gray-600 text-sm mb-4">Get your Certificate of Registration</p>
                            <a href="download-cor.php?id=<?php echo $approved_enrollment['id']; ?>" class="bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors font-medium">
                                Download
                            </a>
                        <?php else: ?>
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-file-alt text-2xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-bold text-gray-800 mb-2">Download COR</h3>
                            <p class="text-gray-600 text-sm mb-4">Available after enrollment approval</p>
                            <button class="bg-gray-300 text-gray-500 px-4 py-2 rounded-lg cursor-not-allowed font-medium" disabled>
                                Not Available
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Enrollment Status -->
            <?php if ($pending_enrollment || $approved_enrollment): ?>
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="bg-gradient-to-r from-green-600 to-green-700 px-6 py-4">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-white"></i>
                            </div>
                            <h2 class="text-xl font-bold text-white">Current Enrollment Status</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php
                        $current_enrollment = $approved_enrollment ?: $pending_enrollment;
                        $status_class = $current_enrollment['status'] === 'approved' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800';
                        $status_text = ucfirst($current_enrollment['status']);
                        $status_icon = $current_enrollment['status'] === 'approved' ? 'fas fa-check-circle' : 'fas fa-clock';
                        ?>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 font-medium">Status:</span>
                                    <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium <?php echo $status_class; ?>">
                                        <i class="<?php echo $status_icon; ?> mr-2"></i>
                                        <?php echo $status_text; ?>
                                    </span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 font-medium">Total Units:</span>
                                    <span class="text-gray-900 font-semibold"><?php echo $current_enrollment['total_units']; ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 font-medium">Total Fees:</span>
                                    <span class="text-gray-900 font-semibold"><?php echo formatCurrency($current_enrollment['total_fees']); ?></span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-gray-600 font-medium">Submitted:</span>
                                    <span class="text-gray-900 font-semibold"><?php echo date('M d, Y g:i A', strtotime($current_enrollment['submitted_at'])); ?></span>
                                </div>
                            </div>
                            <div class="space-y-4">
                                <?php if ($current_enrollment['status'] === 'approved'): ?>
                                    <div class="flex items-center justify-between">
                                        <span class="text-gray-600 font-medium">Approved:</span>
                                        <span class="text-gray-900 font-semibold"><?php echo date('M d, Y g:i A', strtotime($current_enrollment['approved_at'])); ?></span>
                                    </div>
                                    <div class="pt-4">
                                        <a href="download-cor.php?id=<?php echo $current_enrollment['id']; ?>"
                                           class="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium text-center inline-block">
                                            <i class="fas fa-download mr-2"></i>Download Certificate of Registration
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <div class="flex items-center">
                                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                                <i class="fas fa-clock text-yellow-600"></i>
                                            </div>
                                            <div>
                                                <h4 class="text-yellow-800 font-medium">Pending Approval</h4>
                                                <p class="text-yellow-700 text-sm">Your enrollment is being reviewed by the registrar.</p>
                                            </div>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Recent Enrollments -->
            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div class="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-history text-white"></i>
                        </div>
                        <h2 class="text-xl font-bold text-white">Recent Enrollments</h2>
                    </div>
                </div>
                <div class="p-6">
                    <?php if (empty($enrollments)): ?>
                        <div class="text-center py-12">
                            <div class="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-inbox text-4xl text-gray-400"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-800 mb-2">No Enrollments Found</h3>
                            <p class="text-gray-600 mb-6">You haven't enrolled in any subjects yet.</p>
                            <a href="enroll.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                <i class="fas fa-plus mr-2"></i>Start Your First Enrollment
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Period</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subjects</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Fees</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach (array_slice($enrollments, 0, 5) as $enrollment): ?>
                                        <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                            <td class="py-4 px-4">
                                                <div>
                                                    <div class="font-medium text-gray-900"><?php echo getSemesterName($enrollment['semester']); ?></div>
                                                    <div class="text-sm text-gray-500"><?php echo $enrollment['school_year']; ?></div>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <span class="text-gray-900 font-medium"><?php echo $enrollment['subject_count']; ?></span>
                                                <span class="text-gray-500 text-sm">subjects</span>
                                            </td>
                                            <td class="py-4 px-4 text-gray-900 font-medium"><?php echo $enrollment['total_units']; ?></td>
                                            <td class="py-4 px-4 text-gray-900 font-medium"><?php echo formatCurrency($enrollment['total_fees']); ?></td>
                                            <td class="py-4 px-4">
                                                <?php
                                                $status_classes = [
                                                    'approved' => 'bg-green-100 text-green-800',
                                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                                    'returned' => 'bg-red-100 text-red-800'
                                                ];
                                                $status_icons = [
                                                    'approved' => 'fas fa-check-circle',
                                                    'pending' => 'fas fa-clock',
                                                    'returned' => 'fas fa-times-circle'
                                                ];
                                                $status_class = $status_classes[$enrollment['status']] ?? 'bg-gray-100 text-gray-800';
                                                $status_icon = $status_icons[$enrollment['status']] ?? 'fas fa-question-circle';
                                                ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                                    <i class="<?php echo $status_icon; ?> mr-1"></i>
                                                    <?php echo ucfirst($enrollment['status']); ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4 text-gray-900"><?php echo date('M d, Y', strtotime($enrollment['submitted_at'])); ?></td>
                                            <td class="py-4 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="view-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                                                        <i class="fas fa-eye mr-1"></i>View
                                                    </a>
                                                    <?php if ($enrollment['status'] === 'approved'): ?>
                                                        <a href="download-cor.php?id=<?php echo $enrollment['id']; ?>"
                                                           class="text-green-600 hover:text-green-800 text-sm font-medium">
                                                            <i class="fas fa-download mr-1"></i>COR
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <?php if (count($enrollments) > 5): ?>
                            <div class="text-center mt-6 pt-6 border-t border-gray-200">
                                <a href="enrollments.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                                    <i class="fas fa-list mr-2"></i>View All Enrollments
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>



<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn-hover');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 1000);
            }
        });
    });
});
</script>

</body>
</html>
