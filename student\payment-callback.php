<?php
session_start();
require_once '../config/database.php';
require_once '../config/adyen.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Get payment reference from session or URL
$payment_reference_id = $_SESSION['payment_reference_id'] ?? $_GET['ref'] ?? null;
$enrollment_id = $_SESSION['enrollment_id'] ?? $_GET['enrollment_id'] ?? null;
$status = $_GET['status'] ?? null;

// If enrollment_id is not in session/URL, try to extract from reference ID
if (!$enrollment_id && $payment_reference_id) {
    // Reference ID format: NSC_123_timestamp
    if (preg_match('/NSC_(\d+)_/', $payment_reference_id, $matches)) {
        $enrollment_id = $matches[1];
    }
}

if (!$payment_reference_id || !$enrollment_id) {
    // Redirect to dashboard with error message
    $_SESSION['error_message'] = 'Invalid payment callback. Missing reference or enrollment ID.';
    header('Location: dashboard.php');
    exit();
}

try {
    // Check payment status based on URL parameter
    if ($status === 'success') {
        // Payment successful - update enrollment status to pending (awaiting registrar approval)
        $stmt = $conn->prepare("UPDATE enrollments SET
                               status = 'pending',
                               payment_method = 'online',
                               payment_reference = ?,
                               paid_at = NOW()
                               WHERE id = ? AND student_id = ?");

        if ($stmt->execute([$payment_reference_id, $enrollment_id, $_SESSION['user_id']])) {
            // Log activity
            logActivity($_SESSION['user_id'], 'payment_completed', "Online payment completed for enrollment ID: $enrollment_id");

            // Clear session data
            unset($_SESSION['payment_reference_id']);
            unset($_SESSION['enrollment_id']);

            $success_message = 'Payment successful! Your enrollment is now pending registrar approval.';
        } else {
            $error_message = 'Payment completed but failed to update enrollment status. Please contact support.';
        }

    } elseif ($status === 'failed') {
        $error_message = 'Payment failed or was cancelled. Please try again.';

    } else {
        // Payment status unclear
        $error_message = 'Payment status unclear. Please contact support if payment was deducted.';
    }

} catch (Exception $e) {
    $error_message = 'Error verifying payment: ' . $e->getMessage();
}

$page_title = "Payment Status";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50 flex items-center justify-center">
    <div class="max-w-md w-full mx-4">
        <div class="bg-white rounded-2xl shadow-xl p-8 text-center">
            <?php if ($success_message): ?>
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-check text-3xl text-green-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-4">Payment Successful!</h1>
                <p class="text-gray-600 mb-8"><?php echo $success_message; ?></p>
                
                <div class="space-y-4">
                    <a href="view-enrollment.php?id=<?php echo $enrollment_id; ?>" 
                       class="w-full bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium inline-block">
                        <i class="fas fa-eye mr-2"></i>View Enrollment
                    </a>
                    <a href="dashboard.php" 
                       class="w-full border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium inline-block">
                        <i class="fas fa-home mr-2"></i>Back to Dashboard
                    </a>
                </div>
                
            <?php else: ?>
                <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-times text-3xl text-red-600"></i>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 mb-4">Payment Failed</h1>
                <p class="text-gray-600 mb-8"><?php echo $error_message; ?></p>
                
                <div class="space-y-4">
                    <a href="payment-online.php?id=<?php echo $enrollment_id; ?>" 
                       class="w-full bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium inline-block">
                        <i class="fas fa-redo mr-2"></i>Try Again
                    </a>
                    <a href="upload-payment.php?id=<?php echo $enrollment_id; ?>" 
                       class="w-full border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium inline-block">
                        <i class="fas fa-upload mr-2"></i>Upload Payment Proof Instead
                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

</body>
</html>
