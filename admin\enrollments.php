<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get filter parameters
$status_filter = isset($_GET['status']) ? $_GET['status'] : '';
$semester_filter = isset($_GET['semester']) ? (int)$_GET['semester'] : '';
$school_year_filter = isset($_GET['school_year']) ? $_GET['school_year'] : '';

// Build query
$where_conditions = [];
$params = [];

if ($status_filter) {
    $where_conditions[] = "e.status = ?";
    $params[] = $status_filter;
}

if ($semester_filter) {
    $where_conditions[] = "e.semester = ?";
    $params[] = $semester_filter;
}

if ($school_year_filter) {
    $where_conditions[] = "e.school_year = ?";
    $params[] = $school_year_filter;
}

$where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

// Get enrollments
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.student_id, c.course_code, c.course_name,
                       COUNT(es.subject_id) as subject_count
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       LEFT JOIN enrollment_subjects es ON e.id = es.enrollment_id
                       $where_clause
                       GROUP BY e.id
                       ORDER BY e.submitted_at DESC");
$stmt->execute($params);
$enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Get available school years for filter
$stmt = $conn->prepare("SELECT DISTINCT school_year FROM enrollments ORDER BY school_year DESC");
$stmt->execute();
$school_years = $stmt->fetchAll(PDO::FETCH_COLUMN);

// Get current period
$current_period = getCurrentSemesterYear();

$page_title = "Manage Enrollments";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-clipboard-list text-2xl text-nsc-primary"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Manage Enrollments</h1>
                                <p class="text-gray-600">Review and manage student enrollment applications</p>
                            </div>
                        </div>
                        <div class="flex space-x-3">
                            <a href="reports.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                <i class="fas fa-download mr-2"></i>Export Report
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Messages -->
                <?php if (isset($_GET['success'])): ?>
                    <div class="bg-green-50 border border-green-200 text-green-800 px-4 py-3 rounded-lg mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-2"></i>
                            <span><?php echo htmlspecialchars($_GET['success']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (isset($_GET['error'])): ?>
                    <div class="bg-red-50 border border-red-200 text-red-800 px-4 py-3 rounded-lg mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-600 mr-2"></i>
                            <span><?php echo htmlspecialchars($_GET['error']); ?></span>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (isset($_GET['errors'])): ?>
                    <div class="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg mb-6">
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-yellow-600 mr-2 mt-1"></i>
                            <div>
                                <p class="font-medium mb-1">Some errors occurred:</p>
                                <p class="text-sm"><?php echo htmlspecialchars($_GET['errors']); ?></p>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            
                <!-- Filters -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Filter Enrollments</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <form method="GET" action="" class="space-y-6">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div>
                                    <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                                    <select name="status"
                                            id="status"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Status</option>
                                        <option value="pending" <?php echo $status_filter === 'pending' ? 'selected' : ''; ?>>Pending</option>
                                        <option value="approved" <?php echo $status_filter === 'approved' ? 'selected' : ''; ?>>Approved</option>
                                        <option value="returned" <?php echo $status_filter === 'returned' ? 'selected' : ''; ?>>Returned</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="semester" class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                                    <select name="semester"
                                            id="semester"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Semesters</option>
                                        <option value="1" <?php echo $semester_filter === 1 ? 'selected' : ''; ?>>1st Semester</option>
                                        <option value="2" <?php echo $semester_filter === 2 ? 'selected' : ''; ?>>2nd Semester</option>
                                        <option value="3" <?php echo $semester_filter === 3 ? 'selected' : ''; ?>>Summer</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="school_year" class="block text-sm font-medium text-gray-700 mb-2">School Year</label>
                                    <select name="school_year"
                                            id="school_year"
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-nsc-primary focus:border-nsc-primary transition-colors">
                                        <option value="">All Years</option>
                                        <?php foreach ($school_years as $year): ?>
                                            <option value="<?php echo $year; ?>" <?php echo $school_year_filter === $year ? 'selected' : ''; ?>>
                                                <?php echo $year; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="flex items-end">
                                    <button type="submit" class="w-full bg-nsc-primary text-white px-6 py-3 rounded-lg hover:bg-nsc-secondary transition-colors font-medium">
                                        <i class="fas fa-search mr-2"></i>Filter
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            
                <!-- Enrollments Table -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-table text-nsc-primary"></i>
                                </div>
                                <div>
                                    <h2 class="text-xl font-bold text-gray-800">Enrollments</h2>
                                    <div class="flex items-center space-x-2 mt-1">
                                        <?php if ($status_filter): ?>
                                            <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"><?php echo ucfirst($status_filter); ?></span>
                                        <?php endif; ?>
                                        <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium"><?php echo count($enrollments); ?> total</span>
                                    </div>
                                </div>
                            </div>
                            <?php if ($status_filter === 'pending'): ?>
                                <button onclick="bulkApprove()" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                    <i class="fas fa-check mr-2"></i>Bulk Approve
                                </button>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="p-6">
                        <?php if (empty($enrollments)): ?>
                            <div class="text-center py-12">
                                <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="fas fa-inbox text-2xl text-gray-400"></i>
                                </div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">No enrollments found</h3>
                                <p class="text-gray-500">No enrollments match your current filters.</p>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="w-full" id="enrollmentsTable">
                                    <thead>
                                        <tr class="border-b border-gray-200">
                                            <?php if ($status_filter === 'pending'): ?>
                                                <th class="text-left py-3 px-4 font-semibold text-gray-700 w-10">
                                                    <input type="checkbox" id="selectAll" class="w-4 h-4 text-nsc-primary border-gray-300 rounded focus:ring-nsc-primary">
                                                </th>
                                            <?php endif; ?>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Student</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Course</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Period</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Subjects</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Fees</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Submitted</th>
                                            <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                        </tr>
                                    </thead>
                                <tbody>
                                    <?php foreach ($enrollments as $enrollment): ?>
                                        <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                            <?php if ($status_filter === 'pending'): ?>
                                                <td class="py-4 px-4">
                                                    <input type="checkbox" class="w-4 h-4 text-nsc-primary border-gray-300 rounded focus:ring-nsc-primary enrollment-checkbox"
                                                           value="<?php echo $enrollment['id']; ?>">
                                                </td>
                                            <?php endif; ?>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <span class="px-3 py-1 bg-nsc-primary text-white rounded-full text-sm font-medium"><?php echo htmlspecialchars($enrollment['course_code']); ?></span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <p class="text-gray-900"><?php echo getSemesterName($enrollment['semester']); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo $enrollment['school_year']; ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo $enrollment['subject_count']; ?></td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo $enrollment['total_units']; ?></td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo formatCurrency($enrollment['total_fees']); ?></td>
                                            <td class="py-4 px-4">
                                                <?php
                                                $status_class = '';
                                                switch ($enrollment['status']) {
                                                    case 'approved': $status_class = 'bg-green-100 text-green-800'; break;
                                                    case 'pending': $status_class = 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'returned': $status_class = 'bg-red-100 text-red-800'; break;
                                                }
                                                ?>
                                                <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($enrollment['status']); ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <p class="text-gray-900"><?php echo date('M d, Y', strtotime($enrollment['submitted_at'])); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo date('g:i A', strtotime($enrollment['submitted_at'])); ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="review-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                                       class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($enrollment['status'] === 'pending'): ?>
                                                        <a href="review-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                                           class="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-lg text-sm hover:bg-yellow-200 transition-colors" title="Review">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($enrollment['status'] === 'approved'): ?>
                                                        <a href="generate-cor.php?id=<?php echo $enrollment['id']; ?>"
                                                           class="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm hover:bg-green-200 transition-colors" title="Generate COR">
                                                            <i class="fas fa-file-pdf"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Select all functionality
    const selectAllCheckbox = document.getElementById('selectAll');
    const enrollmentCheckboxes = document.querySelectorAll('.enrollment-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            enrollmentCheckboxes.forEach(checkbox => {
                checkbox.checked = this.checked;
            });
        });
    }

    // Update select all when individual checkboxes change
    enrollmentCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const checkedCount = document.querySelectorAll('.enrollment-checkbox:checked').length;
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = checkedCount === enrollmentCheckboxes.length;
                selectAllCheckbox.indeterminate = checkedCount > 0 && checkedCount < enrollmentCheckboxes.length;
            }
        });
    });
});

// Bulk approve function
function bulkApprove() {
    const checkedBoxes = document.querySelectorAll('.enrollment-checkbox:checked');
    if (checkedBoxes.length === 0) {
        showNoSelectionModal();
        return;
    }

    const enrollmentIds = Array.from(checkedBoxes).map(cb => cb.value);
    showBulkApproveModal(enrollmentIds);
}

function showNoSelectionModal() {
    // Create and show no selection modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50';
    modal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">No Selection</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-exclamation-triangle text-yellow-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">Please Select Enrollments</h4>
                            <p class="text-gray-600">You need to select enrollments to approve.</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6">
                        Please check the boxes next to the enrollments you want to approve before clicking the bulk approve button.
                    </p>
                    <button onclick="this.closest('.fixed').remove()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Got it
                    </button>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function showBulkApproveModal(enrollmentIds) {
    // Create and show bulk approve modal
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-gray-600 bg-opacity-50 z-50';
    modal.innerHTML = `
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-xl max-w-md w-full">
                <div class="p-6 border-b border-gray-200">
                    <h3 class="text-lg font-bold text-gray-900">Bulk Approve Enrollments</h3>
                </div>
                <div class="p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                            <i class="fas fa-check-circle text-green-600 text-xl"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900">Confirm Bulk Approval</h4>
                            <p class="text-gray-600">This action will approve multiple enrollments.</p>
                        </div>
                    </div>
                    <p class="text-gray-700 mb-6">
                        You are about to approve <span class="font-semibold text-green-600">${enrollmentIds.length}</span> enrollment(s).
                    </p>
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                            <span class="text-blue-800 text-sm">Students will be notified via email once approved.</span>
                        </div>
                    </div>
                    <div class="flex space-x-3">
                        <button onclick="submitBulkApproval(${JSON.stringify(enrollmentIds)})" class="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            Approve All
                        </button>
                        <button onclick="this.closest('.fixed').remove()" class="flex-1 border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    document.body.appendChild(modal);
}

function submitBulkApproval(enrollmentIds) {
    // Create form and submit
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = 'bulk-approve.php';

    enrollmentIds.forEach(id => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'enrollment_ids[]';
        input.value = id;
        form.appendChild(input);
    });

    document.body.appendChild(form);
    form.submit();
}
</script>

<?php include '../includes/admin_layout_end.php'; ?>
