<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

$error_message = '';
$success_message = '';

// Get student info
$stmt = $conn->prepare("SELECT u.*, c.course_name, c.course_code FROM users u 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE u.id = ?");
$stmt->execute([$_SESSION['user_id']]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Get all courses for dropdown (ensure unique courses)
$stmt = $conn->prepare("SELECT * FROM courses WHERE status = 'active' GROUP BY course_code, course_name ORDER BY course_code");
$stmt->execute();
$courses = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        $action = $_POST['action'];
        
        if ($action === 'update_profile') {
            $first_name = sanitizeInput($_POST['first_name']);
            $last_name = sanitizeInput($_POST['last_name']);
            $middle_name = sanitizeInput($_POST['middle_name']);
            $email = sanitizeInput($_POST['email']);
            $contact_number = sanitizeInput($_POST['contact_number']);
            $address = sanitizeInput($_POST['address']);
            $course_id = (int)$_POST['course_id'];
            $year_level = (int)$_POST['year_level'];
            
            // Validation
            if (empty($first_name) || empty($last_name) || empty($email) || $course_id <= 0 || $year_level <= 0) {
                $error_message = 'Please fill in all required fields.';
            } elseif (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
                $error_message = 'Please enter a valid email address.';
            } else {
                // Check if email is already used by another user
                $stmt = $conn->prepare("SELECT id FROM users WHERE email = ? AND id != ?");
                $stmt->execute([$email, $_SESSION['user_id']]);
                if ($stmt->fetch()) {
                    $error_message = 'Email address is already used by another account.';
                } else {
                    $stmt = $conn->prepare("UPDATE users SET first_name = ?, last_name = ?, middle_name = ?, email = ?, contact_number = ?, address = ?, course_id = ?, year_level = ? WHERE id = ?");
                    if ($stmt->execute([$first_name, $last_name, $middle_name, $email, $contact_number, $address, $course_id, $year_level, $_SESSION['user_id']])) {
                        // Update session variables
                        $_SESSION['first_name'] = $first_name;
                        $_SESSION['last_name'] = $last_name;
                        $_SESSION['email'] = $email;
                        $_SESSION['course_id'] = $course_id;
                        $_SESSION['year_level'] = $year_level;
                        
                        $success_message = 'Profile updated successfully!';
                        logActivity($_SESSION['user_id'], 'profile_updated', 'Student updated profile information');
                        
                        // Refresh student data
                        $stmt = $conn->prepare("SELECT u.*, c.course_name, c.course_code FROM users u 
                                               LEFT JOIN courses c ON u.course_id = c.id 
                                               WHERE u.id = ?");
                        $stmt->execute([$_SESSION['user_id']]);
                        $student = $stmt->fetch(PDO::FETCH_ASSOC);
                    } else {
                        $error_message = 'Failed to update profile. Please try again.';
                    }
                }
            }
        } elseif ($action === 'change_password') {
            $current_password = $_POST['current_password'];
            $new_password = $_POST['new_password'];
            $confirm_password = $_POST['confirm_password'];
            
            if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
                $error_message = 'Please fill in all password fields.';
            } elseif (strlen($new_password) < 6) {
                $error_message = 'New password must be at least 6 characters long.';
            } elseif ($new_password !== $confirm_password) {
                $error_message = 'New passwords do not match.';
            } else {
                // Verify current password
                if (password_verify($current_password, $student['password'])) {
                    $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                    $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                    if ($stmt->execute([$hashed_password, $_SESSION['user_id']])) {
                        $success_message = 'Password changed successfully!';
                        logActivity($_SESSION['user_id'], 'password_changed', 'Student changed password');
                    } else {
                        $error_message = 'Failed to change password. Please try again.';
                    }
                } else {
                    $error_message = 'Current password is incorrect.';
                }
            }
        }
    }
}

$page_title = "My Profile";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-user mr-3 text-green-200 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                        <i class="fas fa-user text-2xl text-green-600"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">My Profile</h1>
                        <p class="text-gray-600">Manage your personal information and account settings</p>
                    </div>
                </div>
            </div>

            <!-- Alert Messages -->
            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg animate-fade-in">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($success_message): ?>
                <div class="bg-green-100 border-l-4 border-green-400 text-green-700 p-4 mb-6 rounded-r-lg animate-fade-in">
                    <div class="flex items-center">
                        <i class="fas fa-check-circle mr-3"></i>
                        <span><?php echo $success_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Profile Information -->
                <div class="lg:col-span-2">
                    <div class="bg-white rounded-2xl shadow-lg p-6 mb-6 card-hover">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-user-edit text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Personal Information</h2>
                        </div>

                        <form method="POST" action="" id="profileForm" class="space-y-6">
                            <input type="hidden" name="action" value="update_profile">

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user mr-2 text-green-600"></i>First Name *
                                    </label>
                                    <input type="text"
                                           id="first_name"
                                           name="first_name"
                                           value="<?php echo htmlspecialchars($student['first_name']); ?>"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                </div>
                                <div>
                                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-user mr-2 text-green-600"></i>Last Name *
                                    </label>
                                    <input type="text"
                                           id="last_name"
                                           name="last_name"
                                           value="<?php echo htmlspecialchars($student['last_name']); ?>"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                </div>
                            </div>

                            <div>
                                <label for="middle_name" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-user mr-2 text-green-600"></i>Middle Name
                                </label>
                                <input type="text"
                                       id="middle_name"
                                       name="middle_name"
                                       value="<?php echo htmlspecialchars($student['middle_name']); ?>"
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                            </div>

                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-envelope mr-2 text-green-600"></i>Email Address *
                                </label>
                                <input type="email"
                                       id="email"
                                       name="email"
                                       value="<?php echo htmlspecialchars($student['email']); ?>"
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="contact_number" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-phone mr-2 text-green-600"></i>Contact Number
                                    </label>
                                    <input type="text"
                                           id="contact_number"
                                           name="contact_number"
                                           value="<?php echo htmlspecialchars($student['contact_number']); ?>"
                                           placeholder="e.g., 09123456789"
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                </div>
                                <div>
                                    <label for="year_level" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-graduation-cap mr-2 text-green-600"></i>Year Level *
                                    </label>
                                    <select id="year_level"
                                            name="year_level"
                                            required
                                            class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                        <option value="">Select Year Level</option>
                                        <option value="1" <?php echo $student['year_level'] == 1 ? 'selected' : ''; ?>>1st Year</option>
                                        <option value="2" <?php echo $student['year_level'] == 2 ? 'selected' : ''; ?>>2nd Year</option>
                                        <option value="3" <?php echo $student['year_level'] == 3 ? 'selected' : ''; ?>>3rd Year</option>
                                        <option value="4" <?php echo $student['year_level'] == 4 ? 'selected' : ''; ?>>4th Year</option>
                                    </select>
                                </div>
                            </div>

                            <div>
                                <label for="course_id" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-book mr-2 text-green-600"></i>Course *
                                </label>
                                <select id="course_id"
                                        name="course_id"
                                        required
                                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="">Select Course</option>
                                    <?php foreach ($courses as $course): ?>
                                        <option value="<?php echo $course['id']; ?>" <?php echo $student['course_id'] == $course['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($course['course_code'] . ' - ' . $course['course_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>

                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-map-marker-alt mr-2 text-green-600"></i>Address
                                </label>
                                <textarea id="address"
                                          name="address"
                                          rows="3"
                                          placeholder="Complete address"
                                          class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors resize-none"><?php echo htmlspecialchars($student['address']); ?></textarea>
                            </div>

                            <button type="submit" class="bg-gradient-to-r from-green-600 to-green-700 text-white px-6 py-3 rounded-lg font-semibold hover:from-green-700 hover:to-green-800 transition-all duration-300 transform hover:scale-105 btn-hover">
                                <i class="fas fa-save mr-2"></i>Update Profile
                            </button>
                        </form>
                    </div>

                    <!-- Change Password -->
                    <div class="bg-white rounded-2xl shadow-lg p-6 card-hover">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-lock text-yellow-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Change Password</h2>
                        </div>

                        <form method="POST" action="" id="passwordForm" class="space-y-6">
                            <input type="hidden" name="action" value="change_password">

                            <div>
                                <label for="current_password" class="block text-sm font-medium text-gray-700 mb-2">
                                    <i class="fas fa-key mr-2 text-yellow-600"></i>Current Password *
                                </label>
                                <input type="password"
                                       id="current_password"
                                       name="current_password"
                                       required
                                       class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors">
                            </div>

                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label for="new_password" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-lock mr-2 text-yellow-600"></i>New Password *
                                    </label>
                                    <input type="password"
                                           id="new_password"
                                           name="new_password"
                                           minlength="6"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors">
                                    <p class="text-sm text-gray-500 mt-1">Minimum 6 characters</p>
                                </div>
                                <div>
                                    <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-2">
                                        <i class="fas fa-lock mr-2 text-yellow-600"></i>Confirm New Password *
                                    </label>
                                    <input type="password"
                                           id="confirm_password"
                                           name="confirm_password"
                                           minlength="6"
                                           required
                                           class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-yellow-500 focus:border-yellow-500 transition-colors">
                                </div>
                            </div>

                            <button type="submit" class="bg-gradient-to-r from-yellow-500 to-yellow-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-yellow-600 hover:to-yellow-700 transition-all duration-300 transform hover:scale-105 btn-hover">
                                <i class="fas fa-key mr-2"></i>Change Password
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Profile Summary -->
                <div class="lg:col-span-1">
                    <div class="bg-white rounded-2xl shadow-lg p-6 card-hover">
                        <div class="flex items-center mb-6">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-id-card text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-900">Profile Summary</h2>
                        </div>

                        <div class="text-center mb-6">
                            <div class="w-20 h-20 bg-gradient-to-br from-green-600 to-green-700 text-white rounded-full flex items-center justify-center mx-auto mb-4 text-2xl font-bold">
                                <?php echo strtoupper(substr($student['first_name'], 0, 1) . substr($student['last_name'], 0, 1)); ?>
                            </div>
                            <h3 class="text-lg font-bold text-gray-900 mb-1"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['last_name']); ?></h3>
                            <p class="text-gray-500"><?php echo htmlspecialchars($student['student_id']); ?></p>
                        </div>

                        <div class="border-t border-gray-200 pt-6">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Course:</span>
                                    <span class="text-sm text-gray-900 font-semibold"><?php echo htmlspecialchars($student['course_code']); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Year Level:</span>
                                    <span class="text-sm text-gray-900 font-semibold"><?php echo $student['year_level']; ?><?php echo getOrdinalSuffix($student['year_level']); ?> Year</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Email:</span>
                                    <span class="text-sm text-gray-900 font-semibold truncate ml-2"><?php echo htmlspecialchars($student['email']); ?></span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Status:</span>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $student['status'] === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'; ?>">
                                        <?php echo ucfirst($student['status']); ?>
                                    </span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-sm font-medium text-gray-600">Member Since:</span>
                                    <span class="text-sm text-gray-900 font-semibold"><?php echo date('M Y', strtotime($student['created_at'])); ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="border-t border-gray-200 pt-6 mt-6">
                            <div class="space-y-3">
                                <a href="dashboard.php" class="w-full flex items-center justify-center px-4 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors">
                                    <i class="fas fa-home mr-2"></i>Dashboard
                                </a>
                                <a href="enrollments.php" class="w-full flex items-center justify-center px-4 py-2 border border-green-300 text-green-700 rounded-lg hover:bg-green-50 transition-colors">
                                    <i class="fas fa-list mr-2"></i>My Enrollments
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password confirmation validation
    const passwordForm = document.getElementById('passwordForm');
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');

    function validatePasswords() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Passwords do not match');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }

    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);

    passwordForm.addEventListener('submit', function(e) {
        if (newPassword.value !== confirmPassword.value) {
            e.preventDefault();
            alert('New passwords do not match!');
            return false;
        }
    });

    // Profile form validation
    const profileForm = document.getElementById('profileForm');
    profileForm.addEventListener('submit', function(e) {
        const firstName = document.getElementById('first_name').value.trim();
        const lastName = document.getElementById('last_name').value.trim();
        const email = document.getElementById('email').value.trim();
        const courseId = document.getElementById('course_id').value;
        const yearLevel = document.getElementById('year_level').value;

        if (!firstName || !lastName || !email || !courseId || !yearLevel) {
            e.preventDefault();
            alert('Please fill in all required fields.');
            return false;
        }
    });
});
</script>



</body>
</html>
