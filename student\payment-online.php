<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get enrollment ID from URL
$enrollment_id = $_GET['id'] ?? null;
if (!$enrollment_id) {
    header('Location: enrollments.php');
    exit();
}

// Get enrollment details
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.student_id 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       WHERE e.id = ? AND e.student_id = ?");
$stmt->execute([$enrollment_id, $_SESSION['user_id']]);
$enrollment = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$enrollment) {
    header('Location: enrollments.php');
    exit();
}

// Check if already paid
if ($enrollment['status'] === 'approved') {
    header('Location: view-enrollment.php?id=' . $enrollment_id);
    exit();
}

$error_message = '';
$success_message = '';

// Redirect to upload payment proof page
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'proceed_to_upload') {
    header('Location: upload-payment.php?id=' . $enrollment_id);
    exit();
}

$page_title = "Upload Payment Proof";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Sidebar (same as other pages) -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>
        
        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-credit-card text-2xl text-green-600"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">Upload Payment Proof</h1>
                            <p class="text-gray-600">Upload your payment receipt for verification</p>
                        </div>
                    </div>
                    <a href="view-enrollment.php?id=<?php echo $enrollment_id; ?>" class="border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                        <i class="fas fa-arrow-left mr-2"></i>Back to Enrollment
                    </a>
                </div>
            </div>

            <?php if ($error_message): ?>
                <div class="bg-red-100 border-l-4 border-red-400 text-red-700 p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center">
                        <i class="fas fa-exclamation-circle mr-3"></i>
                        <span><?php echo $error_message; ?></span>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Payment Summary -->
            <div class="bg-white rounded-2xl shadow-lg mb-8">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Payment Summary</h2>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Student:</span>
                                <span class="font-semibold"><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Student ID:</span>
                                <span class="font-semibold"><?php echo htmlspecialchars($enrollment['student_id']); ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Semester:</span>
                                <span class="font-semibold"><?php echo getSemesterName($enrollment['semester']) . ' ' . $enrollment['school_year']; ?></span>
                            </div>
                        </div>
                        <div class="space-y-4">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Units:</span>
                                <span class="font-semibold"><?php echo $enrollment['total_units']; ?></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Total Amount:</span>
                                <span class="font-bold text-green-600 text-xl"><?php echo formatCurrency($enrollment['total_fees']); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Upload Payment Proof -->
            <div class="bg-white rounded-2xl shadow-lg">
                <div class="p-6 border-b border-gray-200">
                    <h2 class="text-xl font-bold text-gray-900">Upload Payment Proof</h2>
                    <p class="text-gray-600 mt-2">Upload your payment receipt for verification and approval</p>
                </div>
                <div class="p-6">
                    <div class="max-w-md mx-auto mb-8">
                        <!-- Upload Payment Proof -->
                        <div class="border-2 border-green-200 rounded-xl p-8 hover:border-green-300 transition-all">
                            <div class="text-center">
                                <div class="w-24 h-24 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                                    <i class="fas fa-upload text-4xl text-green-600"></i>
                                </div>
                                <h3 class="text-2xl font-bold text-gray-900 mb-4">Upload Payment Proof</h3>
                                <p class="text-gray-600 mb-6">Pay via GCash, Bank Transfer, or Over-the-Counter and upload your receipt</p>

                                <div class="space-y-3 mb-8">
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-mobile-alt mr-3 text-blue-500"></i>
                                        <span>GCash, Maya, GrabPay</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-university mr-3 text-blue-500"></i>
                                        <span>Bank Transfer</span>
                                    </div>
                                    <div class="flex items-center justify-center text-gray-600">
                                        <i class="fas fa-store mr-3 text-blue-500"></i>
                                        <span>Over-the-Counter</span>
                                    </div>
                                </div>

                                <form method="POST" action="">
                                    <input type="hidden" name="action" value="proceed_to_upload">
                                    <button type="submit" class="w-full bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg">
                                        <i class="fas fa-upload mr-2"></i>Upload Payment Proof
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Instructions -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-900 mb-2">
                            <i class="fas fa-info-circle mr-2"></i>Payment Instructions
                        </h4>
                        <ul class="text-blue-800 text-sm space-y-1">
                            <li>• Make your payment using any of the supported methods</li>
                            <li>• Take a clear photo or screenshot of your receipt/confirmation</li>
                            <li>• Upload the payment proof for verification</li>
                            <li>• Wait for registrar approval (usually within 1-2 business days)</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

</body>
</html>
