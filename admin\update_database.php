<?php
// Database Update Script for Records, Registrar, and Accounting Modules
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

$message = '';
$message_type = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Create student_grades table for Records module
        $conn->exec("CREATE TABLE IF NOT EXISTS student_grades (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            subject_id INT NOT NULL,
            semester INT NOT NULL,
            school_year VARCHAR(20) NOT NULL,
            grade DECIMAL(5,2) NOT NULL,
            remarks VARCHAR(50) NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIG<PERSON> KEY (subject_id) REFERENCES subjects(id) ON DELETE CASCADE,
            UNIQUE KEY unique_grade (student_id, subject_id, semester, school_year)
        )");

        // Add verification columns to enrollments table for Registrar module
        $conn->exec("ALTER TABLE enrollments 
                    ADD COLUMN IF NOT EXISTS verification_status VARCHAR(20) DEFAULT 'pending',
                    ADD COLUMN IF NOT EXISTS verification_notes TEXT,
                    ADD COLUMN IF NOT EXISTS verified_at TIMESTAMP NULL,
                    ADD COLUMN IF NOT EXISTS verified_by INT NULL,
                    ADD INDEX idx_verification_status (verification_status)");

        // Create payments table for Accounting module
        $conn->exec("CREATE TABLE IF NOT EXISTS payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            enrollment_id INT NOT NULL,
            amount DECIMAL(10,2) NOT NULL,
            payment_method VARCHAR(50) NOT NULL,
            reference_number VARCHAR(100),
            payment_date DATE NOT NULL,
            status VARCHAR(20) DEFAULT 'confirmed',
            notes TEXT,
            recorded_by INT NOT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (enrollment_id) REFERENCES enrollments(id) ON DELETE CASCADE,
            FOREIGN KEY (recorded_by) REFERENCES users(id),
            INDEX idx_enrollment_id (enrollment_id),
            INDEX idx_payment_date (payment_date),
            INDEX idx_status (status)
        )");

        // Create financial_reports table
        $conn->exec("CREATE TABLE IF NOT EXISTS financial_reports (
            id INT AUTO_INCREMENT PRIMARY KEY,
            report_type VARCHAR(50) NOT NULL,
            report_period VARCHAR(50) NOT NULL,
            total_revenue DECIMAL(12,2) DEFAULT 0,
            total_expenses DECIMAL(12,2) DEFAULT 0,
            net_income DECIMAL(12,2) DEFAULT 0,
            generated_by INT NOT NULL,
            generated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (generated_by) REFERENCES users(id)
        )");

        // Create document_requests table for Registrar
        $conn->exec("CREATE TABLE IF NOT EXISTS document_requests (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            document_type VARCHAR(100) NOT NULL,
            purpose TEXT,
            status VARCHAR(20) DEFAULT 'pending',
            request_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            processed_date TIMESTAMP NULL,
            processed_by INT NULL,
            notes TEXT,
            FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
            FOREIGN KEY (processed_by) REFERENCES users(id),
            INDEX idx_status (status),
            INDEX idx_student_id (student_id)
        )");

        // Insert sample data for testing
        
        // Sample grades
        $sample_grades = [
            [1, 1, 1, '2024-2025', 85.5, 'PASSED'],
            [1, 2, 1, '2024-2025', 92.0, 'PASSED'],
            [1, 3, 1, '2024-2025', 78.5, 'PASSED']
        ];

        $grade_stmt = $conn->prepare("INSERT IGNORE INTO student_grades (student_id, subject_id, semester, school_year, grade, remarks) VALUES (?, ?, ?, ?, ?, ?)");
        foreach ($sample_grades as $grade) {
            $grade_stmt->execute($grade);
        }

        // Sample payments
        $sample_payments = [
            [1, 15000.00, 'cash', 'CASH001', '2024-01-15', 'confirmed', 'Full payment for enrollment', 1],
            [1, 5000.00, 'gcash', 'GC123456789', '2024-01-10', 'confirmed', 'Partial payment', 1]
        ];

        $payment_stmt = $conn->prepare("INSERT IGNORE INTO payments (enrollment_id, amount, payment_method, reference_number, payment_date, status, notes, recorded_by) VALUES (?, ?, ?, ?, ?, ?, ?, ?)");
        foreach ($sample_payments as $payment) {
            $payment_stmt->execute($payment);
        }

        $message = "Database updated successfully! All new tables and columns have been created.";
        $message_type = "success";

    } catch (Exception $e) {
        $message = "Error updating database: " . $e->getMessage();
        $message_type = "error";
    }
}

$page_title = "Database Update";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-2xl w-full p-8">
            <div class="text-center mb-8">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-database text-3xl text-blue-600"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">Database Update</h1>
                <p class="text-gray-600">Update database for Records, Registrar, and Accounting modules</p>
            </div>

            <?php if ($message): ?>
                <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
                    <div class="flex items-center">
                        <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                        <?php echo htmlspecialchars($message); ?>
                    </div>
                </div>
            <?php endif; ?>

            <div class="space-y-6">
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-800 mb-4">What will be updated:</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Create <strong>student_grades</strong> table for Records module
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Add verification columns to <strong>enrollments</strong> table for Registrar module
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Create <strong>payments</strong> table for Accounting module
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Create <strong>financial_reports</strong> table
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Create <strong>document_requests</strong> table for Registrar
                        </li>
                        <li class="flex items-center">
                            <i class="fas fa-check text-green-500 mr-3"></i>
                            Insert sample data for testing
                        </li>
                    </ul>
                </div>

                <?php if (!$message): ?>
                    <form method="POST" class="text-center">
                        <button type="submit" class="bg-blue-600 text-white px-8 py-3 rounded-lg hover:bg-blue-700 transition-colors text-lg font-semibold">
                            <i class="fas fa-play mr-2"></i>Update Database
                        </button>
                    </form>
                <?php else: ?>
                    <div class="text-center">
                        <a href="dashboard.php" class="bg-green-600 text-white px-8 py-3 rounded-lg hover:bg-green-700 transition-colors text-lg font-semibold inline-block">
                            <i class="fas fa-arrow-left mr-2"></i>Back to Dashboard
                        </a>
                    </div>
                <?php endif; ?>
            </div>

            <div class="mt-8 pt-6 border-t border-gray-200 text-center text-sm text-gray-500">
                <p>This update is safe and will not affect existing data.</p>
            </div>
        </div>
    </div>
</body>
</html>
