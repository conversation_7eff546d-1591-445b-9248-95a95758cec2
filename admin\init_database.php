<?php
// Simple database initialization script
// This can be run through the browser to ensure all tables exist

// Database configuration
$host = "localhost";
$username = "root";
$password = "";
$database = "masbate_enrollment";

try {
    // Connect to MySQL
    $pdo = new PDO("mysql:host=$host", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Create database if not exists
    $pdo->exec("CREATE DATABASE IF NOT EXISTS $database");
    $pdo->exec("USE $database");
    
    echo "<h2>Database Initialization</h2>";
    echo "<p>Creating/updating database tables...</p>";
    
    // Create student_grades table
    $pdo->exec("CREATE TABLE IF NOT EXISTS student_grades (
        id INT AUTO_INCREMENT PRIMARY KEY,
        student_id INT NOT NULL,
        subject_id INT NOT NULL,
        semester INT NOT NULL,
        school_year VARCHAR(20) NOT NULL,
        grade DECIMAL(5,2) NOT NULL,
        remarks <PERSON>NUM('PASSED', 'FAILED', 'INCOMPLETE', 'DROPPED') DEFAULT 'PASSED',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
    )");
    echo "<p>✓ student_grades table created/verified</p>";
    
    // Create payments table
    $pdo->exec("CREATE TABLE IF NOT EXISTS payments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        enrollment_id INT NOT NULL,
        amount DECIMAL(10,2) NOT NULL,
        payment_method ENUM('cash', 'check', 'bank_transfer', 'gcash', 'credit_card', 'online_banking') NOT NULL,
        reference_number VARCHAR(100),
        payment_date DATE NOT NULL,
        status ENUM('pending', 'confirmed', 'cancelled') DEFAULT 'confirmed',
        notes TEXT,
        recorded_by INT NOT NULL,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )");
    echo "<p>✓ payments table created/verified</p>";
    
    // Add missing columns to enrollments table
    $columns_to_add = [
        'verification_status' => "ENUM('pending', 'verified', 'rejected') DEFAULT 'pending'",
        'verification_notes' => "TEXT",
        'verified_at' => "TIMESTAMP NULL",
        'verified_by' => "INT NULL",
        'course_id' => "INT NULL"
    ];
    
    foreach ($columns_to_add as $column => $definition) {
        try {
            $result = $pdo->query("SHOW COLUMNS FROM enrollments LIKE '$column'");
            if ($result->rowCount() == 0) {
                $pdo->exec("ALTER TABLE enrollments ADD COLUMN $column $definition");
                echo "<p>✓ Added column '$column' to enrollments table</p>";
            } else {
                echo "<p>✓ Column '$column' already exists in enrollments table</p>";
            }
        } catch (Exception $e) {
            echo "<p>⚠ Error adding column '$column': " . $e->getMessage() . "</p>";
        }
    }
    
    // Check if tables exist and show record counts
    $tables = ['users', 'courses', 'subjects', 'enrollments', 'enrollment_subjects', 'student_grades', 'payments'];
    
    echo "<h3>Table Status:</h3>";
    echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
    echo "<tr><th>Table</th><th>Status</th><th>Record Count</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $result = $pdo->query("SELECT COUNT(*) as count FROM $table");
            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
            echo "<tr><td>$table</td><td style='color: green;'>✓ Exists</td><td>$count</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td>$table</td><td style='color: red;'>✗ Missing</td><td>-</td></tr>";
        }
    }
    echo "</table>";
    
    echo "<h3>Database initialization completed successfully!</h3>";
    echo "<p><a href='dashboard.php'>Go to Dashboard</a></p>";
    
} catch (Exception $e) {
    echo "<h2>Error:</h2>";
    echo "<p style='color: red;'>" . $e->getMessage() . "</p>";
}
?>
