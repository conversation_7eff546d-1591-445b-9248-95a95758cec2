<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Handle actions
$action = $_GET['action'] ?? '';
$message = $_SESSION['message'] ?? '';
$message_type = $_SESSION['message_type'] ?? '';

// Clear session messages after retrieving them
if (isset($_SESSION['message'])) {
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if ($action === 'add_payment') {
        $enrollment_id = $_POST['enrollment_id'];
        $amount = $_POST['amount'];
        $payment_method = $_POST['payment_method'];
        $reference_number = $_POST['reference_number'];
        $payment_date = $_POST['payment_date'];
        $notes = $_POST['notes'];

        try {
            $stmt = $conn->prepare("INSERT INTO payments (enrollment_id, amount, payment_method, reference_number, payment_date, notes, recorded_by, created_at) VALUES (?, ?, ?, ?, ?, ?, ?, NOW())");
            $stmt->execute([$enrollment_id, $amount, $payment_method, $reference_number, $payment_date, $notes, $_SESSION['user_id']]);
            $_SESSION['message'] = "Payment recorded successfully!";
            $_SESSION['message_type'] = "success";
            header('Location: accounting.php');
            exit();
        } catch (Exception $e) {
            $_SESSION['message'] = "Error recording payment: " . $e->getMessage();
            $_SESSION['message_type'] = "error";
            header('Location: accounting.php');
            exit();
        }
    }
}

// Handle GET actions
if ($action === 'export_payments') {
    // Export payments to CSV
    header('Content-Type: text/csv');
    header('Content-Disposition: attachment; filename="payments_' . date('Y-m-d') . '.csv"');

    $output = fopen('php://output', 'w');
    fputcsv($output, ['Payment ID', 'Student ID', 'Student Name', 'Amount', 'Payment Method', 'Reference Number', 'Payment Date', 'Status', 'Recorded By']);

    $payments_stmt = $conn->prepare("SELECT p.*, e.id as enrollment_id, u.student_id, u.first_name, u.last_name, r.first_name as recorder_first, r.last_name as recorder_last
                                    FROM payments p
                                    JOIN enrollments e ON p.enrollment_id = e.id
                                    JOIN users u ON e.student_id = u.id
                                    LEFT JOIN users r ON p.recorded_by = r.id
                                    ORDER BY p.created_at DESC");
    $payments_stmt->execute();
    $all_payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

    foreach ($all_payments as $payment) {
        fputcsv($output, [
            $payment['id'],
            $payment['student_id'],
            $payment['first_name'] . ' ' . $payment['last_name'],
            $payment['amount'],
            $payment['payment_method'],
            $payment['reference_number'],
            $payment['payment_date'],
            $payment['status'],
            $payment['recorder_first'] ? $payment['recorder_first'] . ' ' . $payment['recorder_last'] : ''
        ]);
    }
    fclose($output);
    exit;
} elseif ($action === 'generate_financial_report') {
    // Generate financial report
    require_once '../includes/financial_report_generator.php';
    generateFinancialReport($conn);
    exit;
} elseif ($action === 'send_reminders') {
    // Send payment reminders
    try {
        $unpaid_stmt = $conn->prepare("SELECT e.*, u.student_id, u.first_name, u.last_name, u.email,
                                      (e.total_fees - COALESCE(p.paid_amount, 0)) as balance
                                      FROM enrollments e
                                      JOIN users u ON e.student_id = u.id
                                      LEFT JOIN (SELECT enrollment_id, SUM(amount) as paid_amount FROM payments WHERE status = 'confirmed' GROUP BY enrollment_id) p ON e.id = p.enrollment_id
                                      WHERE e.status = 'approved' AND (e.total_fees - COALESCE(p.paid_amount, 0)) > 0");
        $unpaid_stmt->execute();
        $unpaid_enrollments = $unpaid_stmt->fetchAll(PDO::FETCH_ASSOC);

        $reminder_count = 0;
        foreach ($unpaid_enrollments as $enrollment) {
            // In a real system, you would send actual emails here
            // For now, we'll just count them
            $reminder_count++;
        }

        $_SESSION['message'] = "Payment reminders sent to $reminder_count students with outstanding balances.";
        $_SESSION['message_type'] = "success";
        header('Location: accounting.php');
        exit();
    } catch (Exception $e) {
        $_SESSION['message'] = "Error sending reminders: " . $e->getMessage();
        $_SESSION['message_type'] = "error";
        header('Location: accounting.php');
        exit();
    }
} elseif ($action === 'generate_receipt' && isset($_GET['enrollment_id'])) {
    // Generate payment receipt
    $enrollment_id = $_GET['enrollment_id'];
    require_once '../includes/receipt_generator.php';
    generatePaymentReceipt($conn, $enrollment_id);
    exit;
} elseif ($action === 'payment_history' && isset($_GET['enrollment_id'])) {
    // Show payment history
    $enrollment_id = $_GET['enrollment_id'];
    require_once '../includes/payment_history.php';
    showPaymentHistory($conn, $enrollment_id);
    exit;
}

// Get financial statistics
$stats = [];

// Total revenue
$stmt = $conn->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE status = 'confirmed'");
$stmt->execute();
$stats['total_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Pending payments
$stmt = $conn->prepare("SELECT COALESCE(SUM(total_fees), 0) as total FROM enrollments WHERE status = 'approved' AND id NOT IN (SELECT enrollment_id FROM payments WHERE status = 'confirmed')");
$stmt->execute();
$stats['pending_payments'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// This month revenue
$stmt = $conn->prepare("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE status = 'confirmed' AND MONTH(payment_date) = MONTH(CURRENT_DATE()) AND YEAR(payment_date) = YEAR(CURRENT_DATE())");
$stmt->execute();
$stats['monthly_revenue'] = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Outstanding balance
$stmt = $conn->prepare("SELECT COALESCE(SUM(e.total_fees - COALESCE(p.paid_amount, 0)), 0) as outstanding 
                       FROM enrollments e 
                       LEFT JOIN (SELECT enrollment_id, SUM(amount) as paid_amount FROM payments WHERE status = 'confirmed' GROUP BY enrollment_id) p ON e.id = p.enrollment_id 
                       WHERE e.status = 'approved'");
$stmt->execute();
$stats['outstanding_balance'] = $stmt->fetch(PDO::FETCH_ASSOC)['outstanding'];

// Get enrollments for payment tracking
$search = $_GET['search'] ?? '';
$payment_status = $_GET['payment_status'] ?? '';

$enrollments_query = "SELECT e.*, u.student_id, u.first_name, u.last_name, c.course_code,
                      COALESCE(p.paid_amount, 0) as paid_amount,
                      (e.total_fees - COALESCE(p.paid_amount, 0)) as balance
                      FROM enrollments e 
                      JOIN users u ON e.student_id = u.id 
                      LEFT JOIN courses c ON u.course_id = c.id 
                      LEFT JOIN (SELECT enrollment_id, SUM(amount) as paid_amount FROM payments WHERE status = 'confirmed' GROUP BY enrollment_id) p ON e.id = p.enrollment_id 
                      WHERE e.status = 'approved'";

$params = [];
if ($search) {
    $enrollments_query .= " AND (u.first_name LIKE ? OR u.last_name LIKE ? OR u.student_id LIKE ?)";
    $search_param = "%$search%";
    $params = array_fill(0, 3, $search_param);
}

if ($payment_status === 'paid') {
    $enrollments_query .= " AND e.total_fees <= COALESCE(p.paid_amount, 0)";
} elseif ($payment_status === 'partial') {
    $enrollments_query .= " AND COALESCE(p.paid_amount, 0) > 0 AND e.total_fees > COALESCE(p.paid_amount, 0)";
} elseif ($payment_status === 'unpaid') {
    $enrollments_query .= " AND COALESCE(p.paid_amount, 0) = 0";
}

$enrollments_query .= " ORDER BY e.submitted_at DESC";

$enrollments_stmt = $conn->prepare($enrollments_query);
$enrollments_stmt->execute($params);
$enrollments = $enrollments_stmt->fetchAll(PDO::FETCH_ASSOC);

// Get recent payments
$payments_stmt = $conn->prepare("SELECT p.*, e.id as enrollment_id, u.student_id, u.first_name, u.last_name 
                                FROM payments p 
                                JOIN enrollments e ON p.enrollment_id = e.id 
                                JOIN users u ON e.student_id = u.id 
                                ORDER BY p.created_at DESC 
                                LIMIT 10");
$payments_stmt->execute();
$recent_payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Accounting Office";
?>

<?php include '../includes/admin_layout_start.php'; ?>

<!-- Main Content -->
<div class="flex-1 p-8">
    <!-- Header -->
    <div class="mb-8">
        <div class="bg-gradient-to-r from-emerald-600 to-teal-600 rounded-2xl shadow-xl text-white p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold mb-2">Accounting Office</h1>
                    <p class="text-emerald-100 text-lg">Financial management and payment tracking</p>
                </div>
                <div class="hidden md:block">
                    <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                        <i class="fas fa-calculator text-4xl text-white opacity-80"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php if ($message): ?>
        <div class="mb-6 p-4 rounded-lg <?php echo $message_type === 'success' ? 'bg-green-100 text-green-700 border border-green-200' : 'bg-red-100 text-red-700 border border-red-200'; ?>">
            <div class="flex items-center">
                <i class="fas <?php echo $message_type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'; ?> mr-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        </div>
    <?php endif; ?>

    <!-- Financial Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Total Revenue</p>
                    <h3 class="text-2xl font-bold text-green-600"><?php echo formatCurrency($stats['total_revenue']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-money-bill-wave text-green-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Monthly Revenue</p>
                    <h3 class="text-2xl font-bold text-blue-600"><?php echo formatCurrency($stats['monthly_revenue']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-calendar-alt text-blue-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Outstanding Balance</p>
                    <h3 class="text-2xl font-bold text-red-600"><?php echo formatCurrency($stats['outstanding_balance']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-gray-500 text-sm">Pending Payments</p>
                    <h3 class="text-2xl font-bold text-yellow-600"><?php echo formatCurrency($stats['pending_payments']); ?></h3>
                </div>
                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                    <i class="fas fa-clock text-yellow-600 text-xl"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="flex flex-wrap gap-4 mb-6">
        <button onclick="openAddPaymentModal()" class="bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors flex items-center">
            <i class="fas fa-plus mr-2"></i>Record Payment
        </button>
        <button onclick="generateFinancialReport()" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors flex items-center">
            <i class="fas fa-chart-bar mr-2"></i>Financial Report
        </button>
        <button onclick="exportPayments()" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors flex items-center">
            <i class="fas fa-download mr-2"></i>Export Payments
        </button>
        <button onclick="sendPaymentReminders()" class="bg-orange-600 text-white px-6 py-3 rounded-lg hover:bg-orange-700 transition-colors flex items-center">
            <i class="fas fa-bell mr-2"></i>Send Reminders
        </button>
    </div>

    <!-- Search and Filter -->
    <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
        <form method="GET" class="flex flex-wrap gap-4">
            <div class="flex-1 min-w-64">
                <input type="text" name="search" value="<?php echo htmlspecialchars($search); ?>" 
                       placeholder="Search by student name or ID..." 
                       class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
            </div>
            <div class="min-w-48">
                <select name="payment_status" class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    <option value="">All Payment Status</option>
                    <option value="paid" <?php echo $payment_status === 'paid' ? 'selected' : ''; ?>>Fully Paid</option>
                    <option value="partial" <?php echo $payment_status === 'partial' ? 'selected' : ''; ?>>Partially Paid</option>
                    <option value="unpaid" <?php echo $payment_status === 'unpaid' ? 'selected' : ''; ?>>Unpaid</option>
                </select>
            </div>
            <button type="submit" class="bg-emerald-600 text-white px-6 py-2 rounded-lg hover:bg-emerald-700 transition-colors">
                <i class="fas fa-search mr-2"></i>Search
            </button>
            <a href="accounting.php" class="bg-gray-500 text-white px-6 py-2 rounded-lg hover:bg-gray-600 transition-colors">
                <i class="fas fa-times mr-2"></i>Clear
            </a>
        </form>
    </div>

    <!-- Payment Tracking Table -->
    <div class="bg-white rounded-xl shadow-lg overflow-hidden mb-8">
        <div class="p-6 border-b border-gray-200">
            <h2 class="text-xl font-bold text-gray-800">Payment Tracking</h2>
        </div>
        <div class="overflow-x-auto">
            <?php if (empty($enrollments)): ?>
                <div class="text-center py-12">
                    <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-money-bill-wave text-2xl text-gray-400"></i>
                    </div>
                    <p class="text-gray-500">No payment records found.</p>
                </div>
            <?php else: ?>
                <table class="w-full">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Student</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Course</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Total Fees</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Paid Amount</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Balance</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Status</th>
                            <th class="text-left py-3 px-6 font-semibold text-gray-700">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($enrollments as $enrollment): ?>
                            <tr class="border-b border-gray-100 hover:bg-gray-50">
                                <td class="py-4 px-6">
                                    <div>
                                        <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['last_name'] . ', ' . $enrollment['first_name']); ?></p>
                                        <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                                    </div>
                                </td>
                                <td class="py-4 px-6 text-gray-700"><?php echo htmlspecialchars($enrollment['course_code']); ?></td>
                                <td class="py-4 px-6 text-gray-700"><?php echo formatCurrency($enrollment['total_fees']); ?></td>
                                <td class="py-4 px-6 text-gray-700"><?php echo formatCurrency($enrollment['paid_amount']); ?></td>
                                <td class="py-4 px-6">
                                    <span class="font-semibold <?php echo $enrollment['balance'] > 0 ? 'text-red-600' : 'text-green-600'; ?>">
                                        <?php echo formatCurrency($enrollment['balance']); ?>
                                    </span>
                                </td>
                                <td class="py-4 px-6">
                                    <?php
                                    $payment_status_class = '';
                                    $payment_status_text = '';
                                    if ($enrollment['balance'] <= 0) {
                                        $payment_status_class = 'bg-green-100 text-green-800';
                                        $payment_status_text = 'Paid';
                                    } elseif ($enrollment['paid_amount'] > 0) {
                                        $payment_status_class = 'bg-yellow-100 text-yellow-800';
                                        $payment_status_text = 'Partial';
                                    } else {
                                        $payment_status_class = 'bg-red-100 text-red-800';
                                        $payment_status_text = 'Unpaid';
                                    }
                                    ?>
                                    <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $payment_status_class; ?>">
                                        <?php echo $payment_status_text; ?>
                                    </span>
                                </td>
                                <td class="py-4 px-6">
                                    <div class="flex space-x-2">
                                        <button onclick="recordPayment(<?php echo $enrollment['id']; ?>)" class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-lg text-sm hover:bg-emerald-200 transition-colors" title="Record Payment">
                                            <i class="fas fa-plus"></i>
                                        </button>
                                        <button onclick="viewPaymentHistory(<?php echo $enrollment['id']; ?>)" class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors" title="Payment History">
                                            <i class="fas fa-history"></i>
                                        </button>
                                        <button onclick="generateReceipt(<?php echo $enrollment['id']; ?>)" class="bg-purple-100 text-purple-700 px-3 py-1 rounded-lg text-sm hover:bg-purple-200 transition-colors" title="Generate Receipt">
                                            <i class="fas fa-receipt"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Payment Modal -->
<div id="addPaymentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-bold text-gray-800">Record Payment</h3>
                    <button onclick="closeAddPaymentModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <form id="addPaymentForm" method="POST" action="?action=add_payment" class="p-6" onsubmit="return validatePaymentForm()">
                <input type="hidden" name="enrollment_id" id="payment_enrollment_id">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Amount</label>
                        <input type="number" name="amount" min="0" step="0.01" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Method</label>
                        <select name="payment_method" required class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                            <option value="cash">Cash</option>
                            <option value="check">Check</option>
                            <option value="bank_transfer">Bank Transfer</option>
                            <option value="gcash">GCash</option>
                            <option value="credit_card">Credit Card</option>
                            <option value="online_banking">Online Banking</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Reference Number</label>
                        <input type="text" name="reference_number"
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                               placeholder="Transaction/Check/Reference number">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Payment Date</label>
                        <input type="date" name="payment_date" value="<?php echo date('Y-m-d'); ?>" required
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Notes</label>
                        <textarea name="notes" rows="3"
                                  class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500"
                                  placeholder="Additional notes or remarks..."></textarea>
                    </div>
                </div>
                <div class="flex justify-end space-x-4 mt-6">
                    <button type="button" onclick="closeAddPaymentModal()" class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit" id="paymentSubmitBtn" class="px-4 py-2 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700">
                        Record Payment
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function openAddPaymentModal() {
    document.getElementById('addPaymentModal').classList.remove('hidden');
}



function recordPayment(enrollmentId) {
    if (!enrollmentId) {
        alert('Invalid enrollment ID');
        return;
    }
    document.getElementById('payment_enrollment_id').value = enrollmentId;
    openAddPaymentModal();
}

function viewPaymentHistory(enrollmentId) {
    window.open('?action=payment_history&enrollment_id=' + enrollmentId, '_blank');
}

function generateReceipt(enrollmentId) {
    window.open('?action=generate_receipt&enrollment_id=' + enrollmentId, '_blank');
}

function generateFinancialReport() {
    window.open('?action=generate_financial_report', '_blank');
}

function exportPayments() {
    window.location.href = '?action=export_payments';
}

function sendPaymentReminders() {
    if (confirm('Send payment reminders to all students with outstanding balances?')) {
        window.location.href = '?action=send_reminders';
    }
}

function validatePaymentForm() {
    const submitBtn = document.getElementById('paymentSubmitBtn');
    const amount = document.querySelector('input[name="amount"]').value;
    const paymentMethod = document.querySelector('select[name="payment_method"]').value;
    const paymentDate = document.querySelector('input[name="payment_date"]').value;
    const enrollmentId = document.querySelector('input[name="enrollment_id"]').value;

    if (!enrollmentId) {
        alert('Invalid enrollment ID');
        return false;
    }

    if (!amount || parseFloat(amount) <= 0) {
        alert('Please enter a valid payment amount');
        return false;
    }

    if (!paymentMethod) {
        alert('Please select a payment method');
        return false;
    }

    if (!paymentDate) {
        alert('Please select a payment date');
        return false;
    }

    // Show loading state
    if (submitBtn) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Recording...';
        submitBtn.disabled = true;
    }

    return true;
}

function closeAddPaymentModal() {
    document.getElementById('addPaymentModal').classList.add('hidden');
    // Reset submit button
    const submitBtn = document.getElementById('paymentSubmitBtn');
    if (submitBtn) {
        submitBtn.innerHTML = 'Record Payment';
        submitBtn.disabled = false;
    }
}

// Close modal when clicking outside
document.getElementById('addPaymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeAddPaymentModal();
    }
});
</script>

<?php include '../includes/admin_layout_end.php'; ?>
