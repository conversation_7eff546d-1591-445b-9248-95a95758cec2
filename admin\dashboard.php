<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireAdmin();

$db = new Database();
$conn = $db->getConnection();

// Get current semester and school year
$current_period = getCurrentSemesterYear();

// Get dashboard statistics
$stats = [];

// Total students
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM users WHERE user_type = 'student'");
$stmt->execute();
$stats['total_students'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Total enrollments for current period
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE semester = ? AND school_year = ?");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$stats['current_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Pending enrollments
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE status = 'pending'");
$stmt->execute();
$stats['pending_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Approved enrollments for current period
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM enrollments WHERE status = 'approved' AND semester = ? AND school_year = ?");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$stats['approved_enrollments'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Total subjects
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM subjects WHERE status = 'active'");
$stmt->execute();
$stats['total_subjects'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Total courses
$stmt = $conn->prepare("SELECT COUNT(*) as count FROM courses WHERE status = 'active'");
$stmt->execute();
$stats['total_courses'] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];

// Recent enrollments
$stmt = $conn->prepare("SELECT e.*, u.first_name, u.last_name, u.student_id, c.course_code 
                       FROM enrollments e 
                       JOIN users u ON e.student_id = u.id 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       ORDER BY e.submitted_at DESC 
                       LIMIT 10");
$stmt->execute();
$recent_enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Enrollment status distribution
$stmt = $conn->prepare("SELECT status, COUNT(*) as count FROM enrollments WHERE semester = ? AND school_year = ? GROUP BY status");
$stmt->execute([$current_period['semester'], $current_period['school_year']]);
$status_distribution = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "Admin Dashboard";
$css_path = "../assets/css/style.css";
$js_path = "../assets/js/script.js";
?>

<?php include '../includes/admin_layout_start.php'; ?>

            <!-- Main Content -->
            <div class="flex-1 p-8">
            <!-- Welcome Section -->
            <div class="mb-8">
                <div class="bg-gradient-to-r from-nsc-primary to-nsc-secondary rounded-2xl shadow-xl text-white p-8">
                    <div class="flex items-center justify-between">
                        <div class="flex-1">
                            <h1 class="text-3xl font-bold mb-2">Admin Dashboard</h1>
                            <p class="text-blue-100 text-lg mb-3">Welcome back, <?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>!</p>
                            <div class="flex items-center text-blue-100">
                                <i class="fas fa-calendar-alt mr-2"></i>
                                <span>Current Period: <?php echo getSemesterName($current_period['semester']); ?> - School Year <?php echo $current_period['school_year']; ?></span>
                            </div>
                        </div>
                        <div class="hidden md:block">
                            <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-shield text-4xl text-white opacity-80"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Total Students Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-graduate text-2xl text-nsc-primary"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500 mb-1">Total Students</p>
                            <h3 class="text-2xl font-bold text-nsc-primary"><?php echo number_format($stats['total_students']); ?></h3>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-nsc-primary h-2 rounded-full" style="width: 85%"></div>
                    </div>
                </div>

                <!-- Current Enrollments Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clipboard-list text-2xl text-nsc-secondary"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500 mb-1">Current Enrollments</p>
                            <h3 class="text-2xl font-bold text-nsc-secondary"><?php echo number_format($stats['current_enrollments']); ?></h3>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-nsc-secondary h-2 rounded-full" style="width: 70%"></div>
                    </div>
                </div>

                <!-- Pending Enrollments Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-clock text-2xl text-yellow-600"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500 mb-1">Pending Approvals</p>
                            <h3 class="text-2xl font-bold text-yellow-600"><?php echo number_format($stats['pending_enrollments']); ?></h3>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="w-full bg-gray-200 rounded-full h-2 mr-3">
                            <div class="bg-yellow-500 h-2 rounded-full" style="width: 45%"></div>
                        </div>
                        <?php if ($stats['pending_enrollments'] > 0): ?>
                            <a href="enrollments.php?status=pending" class="bg-yellow-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-yellow-600 transition-colors">Review</a>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Approved Enrollments Card -->
                <div class="bg-white rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:scale-105">
                    <div class="flex items-center justify-between mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-check-circle text-2xl text-green-600"></i>
                        </div>
                        <div class="text-right">
                            <p class="text-sm text-gray-500 mb-1">Approved This Period</p>
                            <h3 class="text-2xl font-bold text-green-600"><?php echo number_format($stats['approved_enrollments']); ?></h3>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: 90%"></div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="mb-8">
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-bolt text-nsc-primary"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Quick Actions</h2>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <a href="enrollments.php?status=pending" class="flex items-center justify-center p-4 bg-yellow-50 border border-yellow-200 rounded-xl hover:bg-yellow-100 transition-all duration-300 transform hover:scale-105 group">
                            <div class="text-center">
                                <i class="fas fa-clock text-2xl text-yellow-600 mb-2 group-hover:scale-110 transition-transform"></i>
                                <p class="text-yellow-800 font-medium">Review Pending</p>
                            </div>
                        </a>
                        <a href="subjects.php" class="flex items-center justify-center p-4 bg-blue-50 border border-blue-200 rounded-xl hover:bg-blue-100 transition-all duration-300 transform hover:scale-105 group">
                            <div class="text-center">
                                <i class="fas fa-book text-2xl text-blue-600 mb-2 group-hover:scale-110 transition-transform"></i>
                                <p class="text-blue-800 font-medium">Manage Subjects</p>
                            </div>
                        </a>
                        <a href="students.php" class="flex items-center justify-center p-4 bg-green-50 border border-green-200 rounded-xl hover:bg-green-100 transition-all duration-300 transform hover:scale-105 group">
                            <div class="text-center">
                                <i class="fas fa-user-graduate text-2xl text-green-600 mb-2 group-hover:scale-110 transition-transform"></i>
                                <p class="text-green-800 font-medium">View Students</p>
                            </div>
                        </a>
                        <a href="reports.php" class="flex items-center justify-center p-4 bg-purple-50 border border-purple-200 rounded-xl hover:bg-purple-100 transition-all duration-300 transform hover:scale-105 group">
                            <div class="text-center">
                                <i class="fas fa-chart-bar text-2xl text-purple-600 mb-2 group-hover:scale-110 transition-transform"></i>
                                <p class="text-purple-800 font-medium">Generate Reports</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            
            <!-- Charts and Overview -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Enrollment Status Distribution -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-chart-pie text-nsc-primary"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Enrollment Status Distribution</h2>
                    </div>
                    <?php if (empty($status_distribution)): ?>
                        <div class="text-center py-8">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-chart-pie text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-gray-500">No enrollment data for current period</p>
                        </div>
                    <?php else: ?>
                        <div class="space-y-4">
                            <?php foreach ($status_distribution as $status): ?>
                                <div class="space-y-2">
                                    <div class="flex justify-between items-center">
                                        <span class="text-gray-700 font-medium capitalize"><?php echo $status['status']; ?></span>
                                        <span class="text-gray-900 font-bold"><?php echo $status['count']; ?></span>
                                    </div>
                                    <div class="w-full bg-gray-200 rounded-full h-3">
                                        <?php
                                        $percentage = ($status['count'] / $stats['current_enrollments']) * 100;
                                        $color = '';
                                        switch ($status['status']) {
                                            case 'approved': $color = 'bg-green-500'; break;
                                            case 'pending': $color = 'bg-yellow-500'; break;
                                            case 'returned': $color = 'bg-red-500'; break;
                                        }
                                        ?>
                                        <div class="<?php echo $color; ?> h-3 rounded-full transition-all duration-500"
                                             style="width: <?php echo $percentage; ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- System Overview -->
                <div class="bg-white rounded-2xl shadow-lg p-6">
                    <div class="flex items-center mb-6">
                        <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-nsc-primary"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">System Overview</h2>
                    </div>
                    <div class="grid grid-cols-2 gap-6 mb-6">
                        <div class="text-center p-4 bg-blue-50 rounded-xl">
                            <h3 class="text-2xl font-bold text-nsc-primary mb-1"><?php echo number_format($stats['total_courses']); ?></h3>
                            <p class="text-gray-600 text-sm">Active Courses</p>
                        </div>
                        <div class="text-center p-4 bg-blue-50 rounded-xl">
                            <h3 class="text-2xl font-bold text-nsc-primary mb-1"><?php echo number_format($stats['total_subjects']); ?></h3>
                            <p class="text-gray-600 text-sm">Active Subjects</p>
                        </div>
                    </div>
                    <div class="border-t border-gray-200 pt-4">
                        <div class="text-center">
                            <p class="text-gray-600 mb-2">System Status</p>
                            <div class="inline-flex items-center px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                                Online
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Enrollments -->
            <div class="bg-white rounded-2xl shadow-lg">
                <div class="flex items-center justify-between p-6 border-b border-gray-200">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-nsc-primary bg-opacity-10 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-history text-nsc-primary"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Recent Enrollments</h2>
                    </div>
                    <a href="enrollments.php" class="bg-nsc-primary text-white px-4 py-2 rounded-lg hover:bg-nsc-secondary transition-colors">
                        View All
                    </a>
                </div>
                <div class="p-6">
                    <?php if (empty($recent_enrollments)): ?>
                        <div class="text-center py-12">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-inbox text-2xl text-gray-400"></i>
                            </div>
                            <p class="text-gray-500">No enrollments found.</p>
                        </div>
                    <?php else: ?>
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Student</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Course</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Period</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Fees</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($recent_enrollments as $enrollment): ?>
                                        <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                            <td class="py-4 px-4">
                                                <div>
                                                    <p class="font-semibold text-gray-900"><?php echo htmlspecialchars($enrollment['first_name'] . ' ' . $enrollment['last_name']); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo htmlspecialchars($enrollment['student_id']); ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo htmlspecialchars($enrollment['course_code']); ?></td>
                                            <td class="py-4 px-4">
                                                <div>
                                                    <p class="text-gray-900"><?php echo getSemesterName($enrollment['semester']); ?></p>
                                                    <p class="text-sm text-gray-500"><?php echo $enrollment['school_year']; ?></p>
                                                </div>
                                            </td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo $enrollment['total_units']; ?></td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo formatCurrency($enrollment['total_fees']); ?></td>
                                            <td class="py-4 px-4">
                                                <?php
                                                $status_class = '';
                                                switch ($enrollment['status']) {
                                                    case 'approved': $status_class = 'bg-green-100 text-green-800'; break;
                                                    case 'pending': $status_class = 'bg-yellow-100 text-yellow-800'; break;
                                                    case 'returned': $status_class = 'bg-red-100 text-red-800'; break;
                                                }
                                                ?>
                                                <span class="px-3 py-1 rounded-full text-sm font-medium <?php echo $status_class; ?>">
                                                    <?php echo ucfirst($enrollment['status']); ?>
                                                </span>
                                            </td>
                                            <td class="py-4 px-4 text-gray-700"><?php echo date('M d, Y', strtotime($enrollment['submitted_at'])); ?></td>
                                            <td class="py-4 px-4">
                                                <div class="flex space-x-2">
                                                    <a href="review-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                                       class="bg-blue-100 text-blue-700 px-3 py-1 rounded-lg text-sm hover:bg-blue-200 transition-colors" title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <?php if ($enrollment['status'] === 'pending'): ?>
                                                        <a href="review-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                                           class="bg-yellow-100 text-yellow-700 px-3 py-1 rounded-lg text-sm hover:bg-yellow-200 transition-colors" title="Review">
                                                            <i class="fas fa-edit"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                    <?php if ($enrollment['status'] === 'approved'): ?>
                                                        <a href="generate-cor.php?id=<?php echo $enrollment['id']; ?>"
                                                           class="bg-green-100 text-green-700 px-3 py-1 rounded-lg text-sm hover:bg-green-200 transition-colors" title="Generate COR">
                                                            <i class="fas fa-file-pdf"></i>
                                                        </a>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            </div>

<?php include '../includes/admin_layout_end.php'; ?>
