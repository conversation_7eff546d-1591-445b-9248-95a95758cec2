<?php
function generatePaymentReceipt($conn, $enrollment_id) {
    // Get enrollment and student info
    $enrollment_stmt = $conn->prepare("SELECT e.*, u.student_id, u.first_name, u.last_name, u.middle_name, u.address, c.course_code, c.course_name 
                                      FROM enrollments e 
                                      JOIN users u ON e.student_id = u.id 
                                      LEFT JOIN courses c ON u.course_id = c.id 
                                      WHERE e.id = ?");
    $enrollment_stmt->execute([$enrollment_id]);
    $enrollment = $enrollment_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$enrollment) {
        echo "Enrollment not found!";
        return;
    }
    
    // Get payments for this enrollment
    $payments_stmt = $conn->prepare("SELECT p.*, u.first_name as recorder_first, u.last_name as recorder_last 
                                    FROM payments p 
                                    LEFT JOIN users u ON p.recorded_by = u.id 
                                    WHERE p.enrollment_id = ? 
                                    ORDER BY p.payment_date DESC");
    $payments_stmt->execute([$enrollment_id]);
    $payments = $payments_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Calculate totals
    $total_paid = array_sum(array_column($payments, 'amount'));
    $balance = $enrollment['total_fees'] - $total_paid;
    
    // Generate HTML receipt
    $html = generateReceiptHTML($enrollment, $payments, $total_paid, $balance);
    
    // Set headers
    header('Content-Type: text/html');
    header('Content-Disposition: inline; filename="receipt_' . $enrollment['student_id'] . '.html"');
    
    echo $html;
}

function generateReceiptHTML($enrollment, $payments, $total_paid, $balance) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Payment Receipt - <?php echo htmlspecialchars($enrollment['student_id']); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; max-width: 800px; margin: 0 auto; padding: 20px; }
            .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #16a34a; padding-bottom: 20px; }
            .school-name { font-size: 24px; font-weight: bold; color: #16a34a; }
            .receipt-title { font-size: 18px; font-weight: bold; margin: 20px 0; }
            .student-info { margin: 20px 0; background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .info-row { display: flex; margin-bottom: 10px; }
            .info-label { font-weight: bold; width: 150px; }
            .payments-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .payments-table th, .payments-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
            .payments-table th { background-color: #f8f9fa; font-weight: bold; }
            .summary { margin: 20px 0; background: #f8f9fa; padding: 15px; border-radius: 8px; }
            .summary-row { display: flex; justify-content: space-between; margin-bottom: 10px; }
            .summary-row.total { font-weight: bold; font-size: 18px; border-top: 2px solid #16a34a; padding-top: 10px; }
            .footer { margin-top: 40px; text-align: center; font-size: 12px; color: #666; }
            @media print { body { margin: 0; } }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="school-name">MASBATE COLLEGES</div>
            <div>Masbate City, Masbate, Philippines</div>
            <div>Tel: (************* | Email: <EMAIL></div>
            <div class="receipt-title">OFFICIAL PAYMENT RECEIPT</div>
        </div>

        <div class="student-info">
            <div class="info-row">
                <div class="info-label">Student Name:</div>
                <div><?php echo htmlspecialchars($enrollment['last_name'] . ', ' . $enrollment['first_name'] . ' ' . $enrollment['middle_name']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Student ID:</div>
                <div><?php echo htmlspecialchars($enrollment['student_id']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Course:</div>
                <div><?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">School Year:</div>
                <div><?php echo htmlspecialchars($enrollment['school_year']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Semester:</div>
                <div><?php echo getSemesterName($enrollment['semester']); ?></div>
            </div>
            <div class="info-row">
                <div class="info-label">Receipt Date:</div>
                <div><?php echo date('F d, Y'); ?></div>
            </div>
        </div>

        <h3>Payment History</h3>
        <table class="payments-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Amount</th>
                    <th>Payment Method</th>
                    <th>Reference Number</th>
                    <th>Recorded By</th>
                    <th>Notes</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($payments)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; color: #666;">No payments recorded</td>
                </tr>
                <?php else: ?>
                    <?php foreach ($payments as $payment): ?>
                    <tr>
                        <td><?php echo date('M d, Y', strtotime($payment['payment_date'])); ?></td>
                        <td><?php echo formatCurrency($payment['amount']); ?></td>
                        <td><?php echo ucfirst(str_replace('_', ' ', $payment['payment_method'])); ?></td>
                        <td><?php echo htmlspecialchars($payment['reference_number']); ?></td>
                        <td><?php echo htmlspecialchars($payment['recorder_first'] . ' ' . $payment['recorder_last']); ?></td>
                        <td><?php echo htmlspecialchars($payment['notes']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>

        <div class="summary">
            <div class="summary-row">
                <div>Total Enrollment Fees:</div>
                <div><?php echo formatCurrency($enrollment['total_fees']); ?></div>
            </div>
            <div class="summary-row">
                <div>Total Payments Made:</div>
                <div><?php echo formatCurrency($total_paid); ?></div>
            </div>
            <div class="summary-row total">
                <div>Outstanding Balance:</div>
                <div style="color: <?php echo $balance > 0 ? '#dc2626' : '#16a34a'; ?>">
                    <?php echo formatCurrency($balance); ?>
                </div>
            </div>
            <?php if ($balance <= 0): ?>
            <div style="text-align: center; margin-top: 15px; color: #16a34a; font-weight: bold;">
                ✓ FULLY PAID
            </div>
            <?php endif; ?>
        </div>

        <div style="margin-top: 50px;">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Cashier</strong>
                        </div>
                    </td>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 1px solid #000; width: 200px; margin: 50px auto 0;">
                            <strong>Student Signature</strong>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div class="footer">
            This is an official payment receipt generated by the Masbate Colleges Student Information System.<br>
            For inquiries, please contact the Accounting Office.
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
