<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get student's enrollment history
$stmt = $conn->prepare("SELECT e.*, COUNT(es.subject_id) as subject_count 
                       FROM enrollments e 
                       LEFT JOIN enrollment_subjects es ON e.id = es.enrollment_id 
                       WHERE e.student_id = ? 
                       GROUP BY e.id 
                       ORDER BY e.submitted_at DESC");
$stmt->execute([$_SESSION['user_id']]);
$enrollments = $stmt->fetchAll(PDO::FETCH_ASSOC);

$page_title = "My Enrollments";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Northern Samar Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-list mr-3 text-green-200 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-book mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
            <!-- Header -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-list text-2xl text-green-600"></i>
                        </div>
                        <div>
                            <h1 class="text-3xl font-bold text-gray-900">My Enrollments</h1>
                            <p class="text-gray-600">View your enrollment history and status</p>
                        </div>
                    </div>
                    <a href="enroll.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                        <i class="fas fa-plus mr-2"></i>New Enrollment
                    </a>
                </div>
            </div>

            <!-- Enrollments -->
            <?php if (empty($enrollments)): ?>
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-12 text-center">
                        <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-inbox text-2xl text-gray-400"></i>
                        </div>
                        <h3 class="text-xl font-bold text-gray-900 mb-2">No Enrollments Found</h3>
                        <p class="text-gray-600 mb-6">You haven't submitted any enrollments yet.</p>
                        <a href="enroll.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium btn-hover">
                            <i class="fas fa-plus mr-2"></i>Start Your First Enrollment
                        </a>
                    </div>
                </div>
            <?php else: ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php foreach ($enrollments as $enrollment): ?>
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden card-hover">
                            <div class="p-4 border-b border-gray-200 flex justify-between items-center">
                                <h3 class="font-semibold text-gray-900">
                                    <?php echo getSemesterName($enrollment['semester']); ?>
                                    <?php echo $enrollment['school_year']; ?>
                                </h3>
                                <?php
                                $status_colors = [
                                    'approved' => 'bg-green-100 text-green-800',
                                    'pending' => 'bg-yellow-100 text-yellow-800',
                                    'returned' => 'bg-red-100 text-red-800'
                                ];
                                $status_class = $status_colors[$enrollment['status']] ?? 'bg-gray-100 text-gray-800';
                                ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?php echo $status_class; ?>">
                                    <?php echo ucfirst($enrollment['status']); ?>
                                </span>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <span class="text-sm text-gray-500">Subjects:</span>
                                        <p class="font-semibold text-gray-900"><?php echo $enrollment['subject_count']; ?> subjects</p>
                                    </div>
                                    <div>
                                        <span class="text-sm text-gray-500">Total Units:</span>
                                        <p class="font-semibold text-gray-900"><?php echo $enrollment['total_units']; ?> units</p>
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <span class="text-sm text-gray-500">Total Fees:</span>
                                    <p class="font-bold text-green-600 text-lg"><?php echo formatCurrency($enrollment['total_fees']); ?></p>
                                </div>

                                <div class="mb-4">
                                    <span class="text-sm text-gray-500">Submitted:</span>
                                    <p class="text-sm text-gray-700"><?php echo date('M d, Y g:i A', strtotime($enrollment['submitted_at'])); ?></p>
                                </div>

                                <?php if ($enrollment['status'] === 'approved' && $enrollment['approved_at']): ?>
                                    <div class="mb-4">
                                        <span class="text-sm text-gray-500">Approved:</span>
                                        <p class="text-sm text-green-600 font-medium"><?php echo date('M d, Y g:i A', strtotime($enrollment['approved_at'])); ?></p>
                                    </div>
                                <?php endif; ?>

                                <?php if ($enrollment['remarks']): ?>
                                    <div class="mb-4">
                                        <span class="text-sm text-gray-500">Remarks:</span>
                                        <p class="text-sm <?php echo $enrollment['status'] === 'returned' ? 'text-red-600' : 'text-blue-600'; ?> font-medium">
                                            <?php echo htmlspecialchars($enrollment['remarks']); ?>
                                        </p>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="p-4 border-t border-gray-200 bg-gray-50">
                                <div class="flex gap-2">
                                    <a href="view-enrollment.php?id=<?php echo $enrollment['id']; ?>"
                                       class="flex-1 text-center px-3 py-2 border border-blue-300 text-blue-700 rounded-lg hover:bg-blue-50 transition-colors text-sm font-medium">
                                        <i class="fas fa-eye mr-1"></i>View Details
                                    </a>

                                    <?php if ($enrollment['status'] === 'approved'): ?>
                                        <a href="download-cor.php?id=<?php echo $enrollment['id']; ?>"
                                           class="flex-1 text-center px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors text-sm font-medium btn-hover">
                                            <i class="fas fa-download mr-1"></i>Download COR
                                        </a>
                                    <?php elseif ($enrollment['status'] === 'pending'): ?>
                                        <button class="flex-1 px-3 py-2 bg-yellow-100 text-yellow-700 rounded-lg cursor-not-allowed text-sm font-medium" disabled>
                                            <i class="fas fa-clock mr-1"></i>Pending
                                        </button>
                                    <?php elseif ($enrollment['status'] === 'returned'): ?>
                                        <button class="flex-1 px-3 py-2 bg-red-100 text-red-700 rounded-lg cursor-not-allowed text-sm font-medium" disabled>
                                            <i class="fas fa-times mr-1"></i>Returned
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>

                <!-- Summary Statistics -->
                <div class="mt-8">
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-6 border-b border-gray-200">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-chart-pie text-blue-600"></i>
                                </div>
                                <h2 class="text-xl font-bold text-gray-900">Enrollment Summary</h2>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 text-center">
                                <?php
                                $total_enrollments = count($enrollments);
                                $approved_count = count(array_filter($enrollments, function($e) { return $e['status'] === 'approved'; }));
                                $pending_count = count(array_filter($enrollments, function($e) { return $e['status'] === 'pending'; }));
                                $returned_count = count(array_filter($enrollments, function($e) { return $e['status'] === 'returned'; }));
                                $total_units = array_sum(array_column($enrollments, 'total_units'));
                                $total_fees = array_sum(array_column($enrollments, 'total_fees'));
                                ?>

                                <div>
                                    <h3 class="text-2xl font-bold text-blue-600"><?php echo $total_enrollments; ?></h3>
                                    <p class="text-gray-600 text-sm">Total Enrollments</p>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-green-600"><?php echo $approved_count; ?></h3>
                                    <p class="text-gray-600 text-sm">Approved</p>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-yellow-600"><?php echo $pending_count; ?></h3>
                                    <p class="text-gray-600 text-sm">Pending</p>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-red-600"><?php echo $returned_count; ?></h3>
                                    <p class="text-gray-600 text-sm">Returned</p>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-indigo-600"><?php echo $total_units; ?></h3>
                                    <p class="text-gray-600 text-sm">Total Units</p>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-green-600"><?php echo formatCurrency($total_fees); ?></h3>
                                    <p class="text-gray-600 text-sm">Total Fees</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn-hover');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 1000);
            }
        });
    });
});
</script>

</body>
</html>
