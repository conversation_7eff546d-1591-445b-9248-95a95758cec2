<?php
function generateEnrollmentCertification($conn, $student_id) {
    // Get student info
    $student_stmt = $conn->prepare("SELECT * FROM users WHERE student_id = ? AND user_type = 'student'");
    $student_stmt->execute([$student_id]);
    $student = $student_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$student) {
        echo "Student not found!";
        return;
    }
    
    // Get current enrollment
    $current_period = getCurrentSemesterYear();
    $enrollment_stmt = $conn->prepare("SELECT e.*, c.course_code, c.course_name 
                                      FROM enrollments e 
                                      LEFT JOIN courses c ON e.course_id = c.id 
                                      WHERE e.student_id = ? AND e.semester = ? AND e.school_year = ? 
                                      AND e.verification_status = 'verified'
                                      ORDER BY e.submitted_at DESC 
                                      LIMIT 1");
    $enrollment_stmt->execute([$student['id'], $current_period['semester'], $current_period['school_year']]);
    $enrollment = $enrollment_stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$enrollment) {
        echo "No verified enrollment found for this student in the current period!";
        return;
    }
    
    // Get enrolled subjects
    $subjects_stmt = $conn->prepare("SELECT es.*, s.subject_code, s.subject_name, s.units 
                                    FROM enrollment_subjects es 
                                    JOIN subjects s ON es.subject_id = s.id 
                                    WHERE es.enrollment_id = ?");
    $subjects_stmt->execute([$enrollment['id']]);
    $subjects = $subjects_stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Generate HTML certification
    $html = generateCertificationHTML($student, $enrollment, $subjects, $current_period);
    
    // Set headers
    header('Content-Type: text/html');
    header('Content-Disposition: inline; filename="enrollment_certification_' . $student_id . '.html"');
    
    echo $html;
}

function generateCertificationHTML($student, $enrollment, $subjects, $current_period) {
    ob_start();
    ?>
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enrollment Certification - <?php echo htmlspecialchars($student['student_id']); ?></title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
            .header { text-align: center; margin-bottom: 40px; }
            .school-name { font-size: 28px; font-weight: bold; color: #16a34a; margin-bottom: 5px; }
            .school-address { font-size: 14px; color: #666; margin-bottom: 2px; }
            .document-title { font-size: 20px; font-weight: bold; margin: 30px 0; text-decoration: underline; }
            .content { margin: 30px 0; text-align: justify; }
            .student-info { background: #f8f9fa; padding: 20px; border-left: 4px solid #16a34a; margin: 20px 0; }
            .subjects-table { width: 100%; border-collapse: collapse; margin: 20px 0; }
            .subjects-table th, .subjects-table td { border: 1px solid #ddd; padding: 10px; text-align: left; }
            .subjects-table th { background-color: #f8f9fa; font-weight: bold; }
            .signature-section { margin-top: 60px; }
            .certification-number { text-align: right; margin-bottom: 20px; font-weight: bold; }
            @media print { body { margin: 20px; } }
        </style>
    </head>
    <body>
        <div class="header">
            <div class="school-name">MASBATE COLLEGES</div>
            <div class="school-address">Masbate City, Masbate, Philippines</div>
            <div class="school-address">Tel: (************* | Email: <EMAIL></div>
            <div class="document-title">CERTIFICATION OF ENROLLMENT</div>
        </div>

        <div class="certification-number">
            Certification No.: MC-<?php echo date('Y'); ?>-<?php echo str_pad($enrollment['id'], 4, '0', STR_PAD_LEFT); ?>
        </div>

        <div class="content">
            <p><strong>TO WHOM IT MAY CONCERN:</strong></p>
            
            <p>This is to certify that <strong><?php echo htmlspecialchars(strtoupper($student['first_name'] . ' ' . $student['middle_name'] . ' ' . $student['last_name'])); ?></strong>, 
            with Student ID Number <strong><?php echo htmlspecialchars($student['student_id']); ?></strong>, 
            is a <strong>bonafide student</strong> of Masbate Colleges for the 
            <strong><?php echo getSemesterName($current_period['semester']); ?></strong> of School Year 
            <strong><?php echo $current_period['school_year']; ?></strong>.</p>
        </div>

        <div class="student-info">
            <h3 style="margin: 0 0 15px 0; color: #16a34a;">Student Information</h3>
            <table style="width: 100%; border: none;">
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Full Name:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($student['first_name'] . ' ' . $student['middle_name'] . ' ' . $student['last_name']); ?></td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Student ID:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($student['student_id']); ?></td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Course:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($enrollment['course_code'] . ' - ' . $enrollment['course_name']); ?></td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Address:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($student['address']); ?></td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Email:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($student['email']); ?></td>
                </tr>
                <tr>
                    <td style="border: none; padding: 5px 0;"><strong>Phone:</strong></td>
                    <td style="border: none; padding: 5px 0;"><?php echo htmlspecialchars($student['phone']); ?></td>
                </tr>
            </table>
        </div>

        <?php if (!empty($subjects)): ?>
        <div>
            <h3 style="color: #16a34a;">Enrolled Subjects</h3>
            <table class="subjects-table">
                <thead>
                    <tr>
                        <th>Subject Code</th>
                        <th>Subject Title</th>
                        <th>Units</th>
                    </tr>
                </thead>
                <tbody>
                    <?php 
                    $total_units = 0;
                    foreach ($subjects as $subject): 
                        $total_units += $subject['units'];
                    ?>
                    <tr>
                        <td><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                        <td><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                        <td><?php echo $subject['units']; ?></td>
                    </tr>
                    <?php endforeach; ?>
                    <tr style="background-color: #f8f9fa; font-weight: bold;">
                        <td colspan="2">TOTAL UNITS</td>
                        <td><?php echo $total_units; ?></td>
                    </tr>
                </tbody>
            </table>
        </div>
        <?php endif; ?>

        <div class="content">
            <p>The student's enrollment has been <strong>officially verified</strong> by the Registrar's Office on 
            <strong><?php echo date('F d, Y', strtotime($enrollment['verified_at'])); ?></strong>.</p>
            
            <p>This certification is issued upon the request of the above-named student for 
            <strong>whatever legal purpose it may serve</strong>.</p>
            
            <p>Given this <strong><?php echo date('jS'); ?></strong> day of <strong><?php echo date('F, Y'); ?></strong> 
            at Masbate Colleges, Masbate City, Masbate, Philippines.</p>
        </div>

        <div class="signature-section">
            <table style="width: 100%;">
                <tr>
                    <td style="width: 50%;"></td>
                    <td style="width: 50%; text-align: center;">
                        <div style="margin-top: 50px; border-top: 2px solid #000; width: 250px; margin: 50px auto 0; padding-top: 10px;">
                            <strong>REGISTRAR</strong><br>
                            <small>Masbate Colleges</small>
                        </div>
                    </td>
                </tr>
            </table>
        </div>

        <div style="margin-top: 40px;">
            <table style="width: 100%; font-size: 12px; color: #666;">
                <tr>
                    <td><strong>Date Issued:</strong> <?php echo date('F d, Y'); ?></td>
                    <td style="text-align: right;"><strong>Valid Until:</strong> <?php echo date('F d, Y', strtotime('+1 year')); ?></td>
                </tr>
            </table>
        </div>

        <div style="margin-top: 30px; text-align: center; font-size: 12px; color: #666; border-top: 1px solid #ddd; padding-top: 15px;">
            This is an official document generated by the Masbate Colleges Student Information System.<br>
            For verification, please contact the Registrar's Office at (************* <NAME_EMAIL>
        </div>
    </body>
    </html>
    <?php
    return ob_get_clean();
}
?>
