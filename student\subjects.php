<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

requireLogin();

$db = new Database();
$conn = $db->getConnection();

// Get student info
$stmt = $conn->prepare("SELECT u.*, c.course_name, c.course_code FROM users u 
                       LEFT JOIN courses c ON u.course_id = c.id 
                       WHERE u.id = ?");
$stmt->execute([$_SESSION['user_id']]);
$student = $stmt->fetch(PDO::FETCH_ASSOC);

// Get filter parameters
$year_filter = isset($_GET['year']) ? (int)$_GET['year'] : $student['year_level'];
$semester_filter = isset($_GET['semester']) ? (int)$_GET['semester'] : 0;

// Get current semester and school year
$current_period = getCurrentSemesterYear();

// Get available subjects for student's course
$where_conditions = ["s.course_id = ?", "s.status = 'active'"];
$params = [$student['course_id']];

if ($year_filter > 0) {
    $where_conditions[] = "s.year_level = ?";
    $params[] = $year_filter;
}
if ($semester_filter > 0) {
    $where_conditions[] = "s.semester = ?";
    $params[] = $semester_filter;
}

$where_clause = "WHERE " . implode(" AND ", $where_conditions);

$stmt = $conn->prepare("SELECT s.* FROM subjects s 
                       $where_clause
                       ORDER BY s.year_level, s.semester, s.subject_code");
$stmt->execute($params);
$subjects = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Group subjects by year and semester
$grouped_subjects = [];
foreach ($subjects as $subject) {
    $grouped_subjects[$subject['year_level']][$subject['semester']][] = $subject;
}

$page_title = "Available Subjects";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">

    <!-- Custom Tailwind Config -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#1e3a8a',
                        'nsc-secondary': '#3b82f6',
                        'nsc-accent': '#f59e0b',
                        'nsc-dark': '#1f2937',
                        'nsc-light': '#f8fafc'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.8s ease-out',
                        'slide-up': 'slideUp 0.8s ease-out',
                        'pulse-slow': 'pulse 3s infinite'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { opacity: '0', transform: 'translateY(20px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        }
                    }
                }
            }
        }
    </script>

    <style>
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .btn-hover {
            transition: all 0.3s ease;
        }
        .btn-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="font-sans antialiased bg-gray-50">

<div class="min-h-screen bg-gray-50">
    <div class="flex">
        <!-- Modern Student Sidebar -->
        <div class="w-64 bg-gradient-to-b from-green-600 to-green-800 min-h-screen shadow-xl">
            <div class="p-6">
                <!-- Student Portal Header -->
                <div class="mb-8">
                    <div class="flex items-center mb-4">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-user-graduate text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-bold text-lg">Student Portal</h3>
                            <p class="text-green-200 text-sm">Enrollment System</p>
                        </div>
                    </div>
                </div>

                <!-- Navigation Menu -->
                <nav class="space-y-2">
                    <a href="dashboard.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-tachometer-alt mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Dashboard</span>
                    </a>
                    <a href="enroll.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-plus-circle mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">New Enrollment</span>
                    </a>
                    <a href="enrollments.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-list mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">My Enrollments</span>
                    </a>
                    <a href="subjects.php" class="flex items-center px-4 py-3 text-white bg-white bg-opacity-20 rounded-lg transition-all duration-300 hover:bg-opacity-30 group">
                        <i class="fas fa-book mr-3 text-green-200 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Available Subjects</span>
                    </a>
                    <a href="profile.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-white hover:bg-opacity-10 hover:text-white group">
                        <i class="fas fa-user mr-3 group-hover:text-white transition-colors"></i>
                        <span class="font-medium">Profile</span>
                    </a>

                    <!-- Logout Button -->
                    <div class="mt-8 pt-4 border-t border-green-500 border-opacity-30">
                        <a href="../logout.php" class="flex items-center px-4 py-3 text-green-200 rounded-lg transition-all duration-300 hover:bg-red-500 hover:bg-opacity-20 hover:text-white group">
                            <i class="fas fa-sign-out-alt mr-3 group-hover:text-white transition-colors"></i>
                            <span class="font-medium">Logout</span>
                        </a>
                    </div>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 p-8">
                <!-- Header -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                <i class="fas fa-book text-2xl text-green-600"></i>
                            </div>
                            <div>
                                <h1 class="text-3xl font-bold text-gray-900">Available Subjects</h1>
                                <p class="text-gray-600">Browse subjects available for your course and year level</p>
                            </div>
                        </div>
                        <a href="enroll.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                            <i class="fas fa-plus mr-2"></i>New Enrollment
                        </a>
                    </div>
                </div>

                <!-- Course Information -->
                <div class="bg-blue-50 border border-blue-200 rounded-2xl p-6 mb-8">
                    <div class="flex items-center">
                        <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                            <i class="fas fa-info-circle text-blue-600"></i>
                        </div>
                        <div class="text-blue-800">
                            <span class="font-semibold">Course:</span> <?php echo htmlspecialchars($student['course_code'] . ' - ' . $student['course_name']); ?> |
                            <span class="font-semibold">Current Year Level:</span> <?php echo $student['year_level']; ?><?php echo getOrdinalSuffix($student['year_level']); ?> Year |
                            <span class="font-semibold">Current Period:</span> <?php echo getSemesterName($current_period['semester']); ?> - <?php echo $current_period['school_year']; ?>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="bg-white rounded-2xl shadow-lg mb-8">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-filter text-green-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Filter Subjects</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <form method="GET" action="" class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <label for="year" class="block text-sm font-medium text-gray-700 mb-2">Year Level</label>
                                <select name="year" id="year" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="">All Years</option>
                                    <option value="1" <?php echo $year_filter == 1 ? 'selected' : ''; ?>>1st Year</option>
                                    <option value="2" <?php echo $year_filter == 2 ? 'selected' : ''; ?>>2nd Year</option>
                                    <option value="3" <?php echo $year_filter == 3 ? 'selected' : ''; ?>>3rd Year</option>
                                    <option value="4" <?php echo $year_filter == 4 ? 'selected' : ''; ?>>4th Year</option>
                                </select>
                            </div>
                            <div>
                                <label for="semester" class="block text-sm font-medium text-gray-700 mb-2">Semester</label>
                                <select name="semester" id="semester" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                    <option value="">All Semesters</option>
                                    <option value="1" <?php echo $semester_filter == 1 ? 'selected' : ''; ?>>1st Semester</option>
                                    <option value="2" <?php echo $semester_filter == 2 ? 'selected' : ''; ?>>2nd Semester</option>
                                    <option value="3" <?php echo $semester_filter == 3 ? 'selected' : ''; ?>>Summer</option>
                                </select>
                            </div>
                            <div class="flex items-end">
                                <div class="w-full space-y-2">
                                    <button type="submit" class="w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                        <i class="fas fa-search mr-2"></i>Filter
                                    </button>
                                    <a href="subjects.php" class="block w-full text-center border border-gray-300 text-gray-700 px-4 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                        <i class="fas fa-times mr-2"></i>Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Subjects Display -->
                <?php if (empty($subjects)): ?>
                    <div class="bg-white rounded-2xl shadow-lg">
                        <div class="p-12 text-center">
                            <div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-book text-2xl text-gray-400"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-gray-900 mb-2">No Subjects Found</h3>
                            <p class="text-gray-500 mb-6">No subjects are available for the selected filters.</p>
                            <a href="subjects.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium">
                                <i class="fas fa-refresh mr-2"></i>View All Subjects
                            </a>
                        </div>
                    </div>
                <?php else: ?>
                    <?php foreach ($grouped_subjects as $year => $semesters): ?>
                        <div class="bg-white rounded-2xl shadow-lg mb-8">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center">
                                    <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                                        <i class="fas fa-graduation-cap text-green-600"></i>
                                    </div>
                                    <h2 class="text-xl font-bold text-gray-800">
                                        <?php echo $year; ?><?php echo getOrdinalSuffix($year); ?> Year Subjects
                                    </h2>
                                </div>
                            </div>
                            <div class="p-6">
                                <?php foreach ($semesters as $semester => $semester_subjects): ?>
                                    <div class="mb-8">
                                        <h3 class="text-lg font-semibold text-green-600 mb-4 flex items-center">
                                            <i class="fas fa-calendar mr-2"></i>
                                            <?php echo getSemesterName($semester); ?>
                                        </h3>

                                        <div class="overflow-x-auto">
                                            <table class="w-full">
                                                <thead>
                                                    <tr class="border-b border-gray-200">
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Code</th>
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Subject Name</th>
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Prerequisites</th>
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Fee</th>
                                                        <th class="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <?php
                                                    $rate_per_unit = 500;
                                                    foreach ($semester_subjects as $subject):
                                                    ?>
                                                        <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                                                            <td class="py-4 px-4 font-semibold text-green-600"><?php echo htmlspecialchars($subject['subject_code']); ?></td>
                                                            <td class="py-4 px-4 text-gray-900"><?php echo htmlspecialchars($subject['subject_name']); ?></td>
                                                            <td class="py-4 px-4">
                                                                <span class="px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm font-medium"><?php echo $subject['units']; ?> units</span>
                                                            </td>
                                                            <td class="py-4 px-4">
                                                                <?php if ($subject['prerequisite']): ?>
                                                                    <span class="text-yellow-600 flex items-center">
                                                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                                                        <?php echo htmlspecialchars($subject['prerequisite']); ?>
                                                                    </span>
                                                                <?php else: ?>
                                                                    <span class="text-gray-400">None</span>
                                                                <?php endif; ?>
                                                            </td>
                                                            <td class="py-4 px-4 font-semibold text-green-600">
                                                                <?php echo formatCurrency($subject['units'] * $rate_per_unit); ?>
                                                            </td>
                                                            <td class="py-4 px-4">
                                                                <?php if ($year == $student['year_level'] && $semester == $current_period['semester']): ?>
                                                                    <span class="px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">Available for Enrollment</span>
                                                                <?php elseif ($year < $student['year_level'] || ($year == $student['year_level'] && $semester < $current_period['semester'])): ?>
                                                                    <span class="px-3 py-1 bg-gray-100 text-gray-800 rounded-full text-sm font-medium">Previous Period</span>
                                                                <?php else: ?>
                                                                    <span class="px-3 py-1 bg-yellow-100 text-yellow-800 rounded-full text-sm font-medium">Future Period</span>
                                                                <?php endif; ?>
                                                            </td>
                                                        </tr>
                                                    <?php endforeach; ?>
                                                </tbody>
                                                <tfoot>
                                                    <tr class="bg-gray-50 border-t border-gray-200">
                                                        <th class="py-3 px-4 font-semibold text-gray-700" colspan="2">
                                                            Total for <?php echo getSemesterName($semester); ?>
                                                        </th>
                                                        <th class="py-3 px-4 font-semibold text-gray-700">
                                                            <?php echo array_sum(array_column($semester_subjects, 'units')); ?> units
                                                        </th>
                                                        <th class="py-3 px-4"></th>
                                                        <th class="py-3 px-4 font-semibold text-green-600">
                                                            <?php echo formatCurrency(array_sum(array_column($semester_subjects, 'units')) * $rate_per_unit); ?>
                                                        </th>
                                                        <th class="py-3 px-4"></th>
                                                    </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>

                                    <?php if ($semester !== array_key_last($semesters)): ?>
                                        <div class="border-t border-gray-200 my-6"></div>
                                    <?php endif; ?>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endforeach; ?>
                
                <!-- Summary Information -->
                <div class="bg-white rounded-2xl shadow-lg">
                    <div class="p-6 border-b border-gray-200">
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                                <i class="fas fa-info-circle text-blue-600"></i>
                            </div>
                            <h2 class="text-xl font-bold text-gray-800">Important Information</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                            <div>
                                <h3 class="text-lg font-semibold text-green-600 mb-4">Fee Structure</h3>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Rate per unit: <strong><?php echo formatCurrency(500); ?></strong></span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">Miscellaneous fee: <strong><?php echo formatCurrency(2000); ?></strong></span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-600 mr-3"></i>
                                        <span class="text-gray-700">Total fee = (Units × Rate) + Miscellaneous fee</span>
                                    </li>
                                </ul>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-green-600 mb-4">Enrollment Guidelines</h3>
                                <ul class="space-y-2">
                                    <li class="flex items-center">
                                        <i class="fas fa-check text-green-600 mr-3"></i>
                                        <span class="text-gray-700">You can only enroll in subjects for your current year and semester</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-exclamation-triangle text-yellow-600 mr-3"></i>
                                        <span class="text-gray-700">Prerequisites must be completed before enrolling</span>
                                    </li>
                                    <li class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-600 mr-3"></i>
                                        <span class="text-gray-700">Contact the registrar for special cases</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="text-center">
                            <a href="enroll.php" class="bg-green-600 text-white px-8 py-4 rounded-lg hover:bg-green-700 transition-colors font-medium text-lg">
                                <i class="fas fa-plus mr-2"></i>Start New Enrollment
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Add any JavaScript functionality here
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth animations
    const cards = document.querySelectorAll('.card-hover');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
        });
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add loading states for buttons
    const buttons = document.querySelectorAll('.btn-hover');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (!this.disabled) {
                this.style.opacity = '0.7';
                setTimeout(() => {
                    this.style.opacity = '1';
                }, 1000);
            }
        });
    });
});
</script>

</body>
</html>
