<?php
session_start();
require_once '../config/database.php';
require_once '../includes/functions.php';

// For testing purposes, simulate admin login if not logged in
if (!isset($_SESSION['user_id'])) {
    $_SESSION['user_id'] = 1;
    $_SESSION['user_type'] = 'admin';
    $_SESSION['first_name'] = 'Test';
    $_SESSION['last_name'] = 'Admin';
}

$db = new Database();
$conn = $db->getConnection();

$page_title = "Action Testing";
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?> - Masbate Colleges</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'nsc-primary': '#16a34a',
                        'nsc-secondary': '#22c55e'
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <div class="min-h-screen">
        <div class="max-w-6xl mx-auto p-6">
            <!-- Header -->
            <div class="bg-gradient-to-r from-green-600 to-emerald-600 rounded-2xl shadow-xl text-white p-8 mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-3xl font-bold mb-2">Action Testing</h1>
                        <p class="text-green-100 text-lg">Test all CRUD operations for Records, Registrar, and Accounting</p>
                    </div>
                    <div class="hidden md:block">
                        <div class="w-24 h-24 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-cogs text-4xl text-white opacity-80"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Actions -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <!-- Records Testing -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-folder-open text-green-600 text-xl"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Records Module</h2>
                    </div>
                    <div class="space-y-3">
                        <a href="records.php" class="block w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-center">
                            <i class="fas fa-eye mr-2"></i>View Records
                        </a>
                        <button onclick="testAddGrade()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Test Add Grade
                        </button>
                        <button onclick="testEditGrade()" class="w-full bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors">
                            <i class="fas fa-edit mr-2"></i>Test Edit Grade
                        </button>
                        <button onclick="testDeleteGrade()" class="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                            <i class="fas fa-trash mr-2"></i>Test Delete Grade
                        </button>
                    </div>
                </div>

                <!-- Registrar Testing -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-stamp text-purple-600 text-xl"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Registrar Module</h2>
                    </div>
                    <div class="space-y-3">
                        <a href="registrar.php" class="block w-full bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors text-center">
                            <i class="fas fa-eye mr-2"></i>View Registrar
                        </a>
                        <button onclick="testVerifyEnrollment()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-check mr-2"></i>Test Verify
                        </button>
                        <button onclick="testBulkVerify()" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors">
                            <i class="fas fa-check-double mr-2"></i>Test Bulk Verify
                        </button>
                        <button onclick="testExportList()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>Test Export
                        </button>
                    </div>
                </div>

                <!-- Accounting Testing -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-emerald-100 rounded-lg flex items-center justify-center mr-4">
                            <i class="fas fa-calculator text-emerald-600 text-xl"></i>
                        </div>
                        <h2 class="text-xl font-bold text-gray-800">Accounting Module</h2>
                    </div>
                    <div class="space-y-3">
                        <a href="accounting.php" class="block w-full bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors text-center">
                            <i class="fas fa-eye mr-2"></i>View Accounting
                        </a>
                        <button onclick="testAddPayment()" class="w-full bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-plus mr-2"></i>Test Add Payment
                        </button>
                        <button onclick="testSendReminders()" class="w-full bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors">
                            <i class="fas fa-bell mr-2"></i>Test Send Reminders
                        </button>
                        <button onclick="testExportPayments()" class="w-full bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                            <i class="fas fa-download mr-2"></i>Test Export
                        </button>
                    </div>
                </div>
            </div>

            <!-- Database Status -->
            <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Database Status</h2>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <?php
                    $tables = ['users', 'student_grades', 'enrollments', 'payments'];
                    foreach ($tables as $table) {
                        try {
                            $result = $conn->query("SELECT COUNT(*) as count FROM $table");
                            $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
                            echo "<div class='text-center p-3 bg-green-50 rounded-lg'>";
                            echo "<div class='text-2xl font-bold text-green-600'>$count</div>";
                            echo "<div class='text-sm text-gray-600'>$table</div>";
                            echo "</div>";
                        } catch (Exception $e) {
                            echo "<div class='text-center p-3 bg-red-50 rounded-lg'>";
                            echo "<div class='text-2xl font-bold text-red-600'>Error</div>";
                            echo "<div class='text-sm text-gray-600'>$table</div>";
                            echo "</div>";
                        }
                    }
                    ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="bg-white rounded-xl shadow-lg p-6">
                <h2 class="text-xl font-bold text-gray-800 mb-4">Quick Actions</h2>
                <div class="flex flex-wrap gap-4">
                    <a href="init_database.php" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
                        <i class="fas fa-database mr-2"></i>Initialize Database
                    </a>
                    <a href="insert_sample_data.php" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                        <i class="fas fa-plus mr-2"></i>Insert Sample Data
                    </a>
                    <a href="test_functionality.php" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                        <i class="fas fa-vial mr-2"></i>Run Tests
                    </a>
                    <a href="dashboard.php" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fas fa-home mr-2"></i>Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        function testAddGrade() {
            window.open('records.php', '_blank');
            alert('Click the "Add Grade" button to test the add functionality');
        }

        function testEditGrade() {
            window.open('records.php', '_blank');
            alert('Click the edit button (pencil icon) on any grade record to test edit functionality');
        }

        function testDeleteGrade() {
            window.open('records.php', '_blank');
            alert('Click the delete button (trash icon) on any grade record to test delete functionality');
        }

        function testVerifyEnrollment() {
            window.open('registrar.php', '_blank');
            alert('Click the verify button (stamp icon) on any enrollment to test verification');
        }

        function testBulkVerify() {
            window.open('registrar.php', '_blank');
            alert('Click the "Bulk Verification" button to test bulk verification');
        }

        function testExportList() {
            window.open('registrar.php', '_blank');
            alert('Click the "Export Verified List" button to test export functionality');
        }

        function testAddPayment() {
            window.open('accounting.php', '_blank');
            alert('Click the "Record Payment" button or the plus icon on any enrollment to test payment recording');
        }

        function testSendReminders() {
            window.open('accounting.php', '_blank');
            alert('Click the "Send Reminders" button to test reminder functionality');
        }

        function testExportPayments() {
            window.open('accounting.php', '_blank');
            alert('Click the "Export Payments" button to test export functionality');
        }
    </script>
</body>
</html>
